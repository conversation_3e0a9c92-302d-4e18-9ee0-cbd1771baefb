<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>2.7.8</version>
    <relativePath/> <!-- lookup parent from repository -->
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.iqiyi.v</groupId>
  <artifactId>v-spring-cloud-netflix</artifactId>
  <packaging>pom</packaging>
  <version>v-1.4.2-boot-2.7.x</version>
  <properties>
    <java.version>8</java.version>
    <spring-cloud.version>2021.0.5</spring-cloud.version>
    <iqiyi-http-client.version>2.1.8</iqiyi-http-client.version>
  </properties>
  <modules>
    <module>v-spring-cloud-netflix-eureka-client</module>
    <module>v-spring-cloud-commons</module>
  </modules>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-dependencies</artifactId>
        <version>${spring-cloud.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.iqiyi.v</groupId>
        <artifactId>v-spring-cloud-commons</artifactId>
        <version>v-1.4.2-boot-2.7.x</version>
      </dependency>
      <dependency>
        <groupId>com.iqiyi.v</groupId>
        <artifactId>v-spring-cloud-netflix-eureka-client</artifactId>
        <version>v-1.4.2-boot-2.7.x</version>
      </dependency>
      <dependency>
        <groupId>org.apache.skywalking</groupId>
        <artifactId>apm-toolkit-trace</artifactId>
        <version>6.1.0-iqiyi-9</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <distributionManagement>
    <repository>
      <id>iqiyi-maven-release</id>
      <name>iqiyi-maven-release</name>
      <url>http://jfrog.cloud.qiyi.domain/iqiyi-maven-release</url>
    </repository>
    <snapshotRepository>
      <id>iqiyi-maven-snapshot</id>
      <name>iqiyi-maven-snapshot</name>
      <url>http://jfrog.cloud.qiyi.domain/iqiyi-maven-snapshot</url>
    </snapshotRepository>
  </distributionManagement>

  <repositories>
    <repository>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>libs-release</id>
      <name>libs-release</name>
      <url>http://jfrog.cloud.qiyi.domain/libs-release</url>
    </repository>
    <repository>
      <snapshots/>
      <id>libs-snapshot</id>
      <name>libs-snapshot</name>
      <url>http://jfrog.cloud.qiyi.domain/libs-snapshot</url>
    </repository>
    <repository>
      <snapshots/>
      <id>iqiyi-maven-cloudservice</id>
      <name>iqiyi-maven-cloudservice</name>
      <url>http://jfrog.cloud.qiyi.domain/iqiyi-maven-cloudservice</url>
    </repository>
  </repositories>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <executions>
          <execution>
            <id>attach-sources</id>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>versions-maven-plugin</artifactId>
        <version>2.2</version>
        <configuration>
          <!-- 不生成备份文件 -->
          <generateBackupPoms>false</generateBackupPoms>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>