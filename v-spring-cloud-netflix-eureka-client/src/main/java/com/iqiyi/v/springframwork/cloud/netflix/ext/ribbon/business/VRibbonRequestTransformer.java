package com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business;

import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.loadbalancer.LoadBalancerRequestTransformer;
import org.springframework.http.HttpRequest;

/**
 * 保存serviceInstance到Http中
 */
public class VRibbonRequestTransformer implements LoadBalancerRequestTransformer {


    @Override
    public HttpRequest transformRequest(HttpRequest request, ServiceInstance instance) {
        if (instance != null) {
            VRibbonHttpContext.saveServiceInstance(request, instance);
        }
        return request;
    }
}
