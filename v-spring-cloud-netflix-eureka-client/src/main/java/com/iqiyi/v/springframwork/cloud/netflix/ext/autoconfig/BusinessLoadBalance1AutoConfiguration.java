package com.iqiyi.v.springframwork.cloud.netflix.ext.autoconfig;

import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.loadbalance.support.VLoadBalancerClientFactory;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.loadbalancer.LoadBalancerClientsProperties;
import org.springframework.cloud.loadbalancer.annotation.LoadBalancerClientSpecification;
import org.springframework.cloud.loadbalancer.annotation.LoadBalancerClients;
import org.springframework.cloud.loadbalancer.config.LoadBalancerAutoConfiguration;
import org.springframework.cloud.loadbalancer.support.LoadBalancerClientFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Collections;
import java.util.List;

/**
 * BusinessLoadBalanceAutoConfiguration 扩展，由于 BusinessLoadBalanceAutoConfiguration 无法添加
 * @AutoConfigureBefore(value = {LoadBalancerAutoConfiguration.class}) 注解，会出现循环依赖，推测可能原因是
 * LoadBalancerAutoConfiguration 名字重复问题导致，BusinessLoadBalanceAutoConfiguration 中也同样使用该类 LoadBalancerAutoConfiguration
 * 排序，暂先再创建一个自动配置类
 *
 * <AUTHOR>
 * @version 1.0
 * @Desc
 * @date 2023/2/8 20:19
 */
@Configuration(proxyBeanMethods = false)
@LoadBalancerClients
@EnableConfigurationProperties(LoadBalancerClientsProperties.class)
@AutoConfigureBefore(value = {LoadBalancerAutoConfiguration.class})
public class BusinessLoadBalance1AutoConfiguration {

    private final ObjectProvider<List<LoadBalancerClientSpecification>> configurations;

    public BusinessLoadBalance1AutoConfiguration(ObjectProvider<List<LoadBalancerClientSpecification>> configurations) {
        this.configurations = configurations;
    }

    @ConditionalOnMissingBean
    @Bean
    public LoadBalancerClientFactory loadBalancerClientFactory(LoadBalancerClientsProperties properties) {
        LoadBalancerClientFactory clientFactory = new VLoadBalancerClientFactory(properties);
        clientFactory.setConfigurations(this.configurations.getIfAvailable(Collections::emptyList));
        return clientFactory;
    }

}
