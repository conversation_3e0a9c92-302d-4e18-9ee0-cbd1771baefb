package com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.loadbalancer.reactive.ReactiveLoadBalancer;

import java.net.URI;
import java.util.List;

import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.loadbalance.core.VRoundRobinLoadBalancer;
import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.p.VLoadBalanceProperties;

import static com.iqiyi.v.springframwork.cloud.client.constants.VDefinedHttpStatus.SERVER_CLOSING_HTTP_STATUS;

/**
 * 剔除Server工具类
 *
 * <AUTHOR>
 * @date 2022/5/31 15:12
 */
@Slf4j
public final class VEvictionServers {

    /**
     * 是排除
     */
    public static boolean isEviction(Integer rawStatusCode) {
        return SERVER_CLOSING_HTTP_STATUS.getStatus().equals(rawStatusCode);
    }

    public static void evictionServ(String serviceId, String host, int port, URI uri, Integer rawStatusCode,
        ReactiveLoadBalancer<ServiceInstance> loadBalancer, VLoadBalanceProperties vLoadBalanceProperties) {
        if (isEviction(rawStatusCode) && loadBalancer instanceof VRoundRobinLoadBalancer) {
            VRoundRobinLoadBalancer vLoadBalancer = (VRoundRobinLoadBalancer) loadBalancer;
            vLoadBalancer.getAllServiceInstancesFlux().subscribe(
                serviceInstances -> {
                    if (vLoadBalanceProperties.isEnable(serviceInstances.size(), vLoadBalancer.getReachableServiceInstances().size())) { // Http 状态码、ZoneAwareLoadBalancer、可开启
                        vLoadBalancer.addRecentlyEvictionServers(host, port, serviceId);
                        final List<ServiceInstance> reachableServers = vLoadBalancer.getReachableServiceInstances();
                            log.info("v-load-balance reachable servers size: {}, one server({}) down, surplus up server size: {}",
                                reachableServers.size(), uri, reachableServers.size() - 1);
                    }
                }
            );
        }
    }

    public static String getServiceInstanceId(ServiceInstance serviceInstance) {
        return serviceInstance.getHost() + ":" + serviceInstance.getPort();
    }

    private VEvictionServers() {
        throw new RuntimeException("不允许创建");
    }

}
