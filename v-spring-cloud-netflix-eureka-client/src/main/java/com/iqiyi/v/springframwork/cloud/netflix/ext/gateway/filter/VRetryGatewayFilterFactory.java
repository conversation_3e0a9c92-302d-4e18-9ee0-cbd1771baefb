package com.iqiyi.v.springframwork.cloud.netflix.ext.gateway.filter;

import com.google.common.collect.Lists;
import com.iqiyi.v.springframwork.cloud.client.constants.VDefinedHttpStatus;
import com.iqiyi.v.springframwork.cloud.client.constants.VEurekaClientConstants;
import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.utils.VLogs;
import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.utils.VServerWebExchangeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.reactivestreams.Publisher;
import org.springframework.cloud.gateway.event.EnableBodyCachingEvent;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.cloud.gateway.filter.factory.RetryGatewayFilterFactory;
import org.springframework.cloud.gateway.filter.factory.RetryGatewayFilterFactory.BackoffConfig;
import org.springframework.cloud.gateway.support.ServerWebExchangeUtils;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.util.CollectionUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.netty.Connection;
import reactor.retry.*;

import java.util.List;
import java.util.function.Predicate;
import java.util.function.Supplier;

import static org.springframework.cloud.gateway.filter.factory.RetryGatewayFilterFactory.RETRY_ITERATION_KEY;
import static org.springframework.cloud.gateway.support.GatewayToStringStyler.filterToStringCreator;

/**
 * 会员重试策略
 * <AUTHOR>
 * @date 2022/5/6 18:25
 */
@Slf4j
public class VRetryGatewayFilterFactory extends AbstractGatewayFilterFactory<RetryGatewayFilterFactory.RetryConfig> {

    private RetryGatewayFilterFactory retryGatewayFilterFactory;

    public VRetryGatewayFilterFactory() {
        super(RetryGatewayFilterFactory.RetryConfig.class);
        this.retryGatewayFilterFactory = new RetryGatewayFilterFactory();
    }

    @Override
    public GatewayFilter apply(RetryGatewayFilterFactory.RetryConfig retryConfig) {
        try {
            Repeat<ServerWebExchange> statusCodeRepeat = null;
            if (!retryConfig.getStatuses().isEmpty() || !retryConfig.getSeries().isEmpty()) {
                Predicate<RepeatContext<ServerWebExchange>> repeatPredicate = context -> {
                    ServerWebExchange exchange = context.applicationContext();
                    if (exceedsMaxIterations(exchange, retryConfig)) {
                        return false;
                    }

                    final ServerHttpResponse response = exchange.getResponse();
                    List<String> list = exchange.getResponse() != null && exchange.getResponse().getHeaders() != null
                            && exchange.getResponse().getHeaders().get(VEurekaClientConstants.RES_INSTANCE_STATUS_KEY) != null ?
                            exchange.getResponse().getHeaders().get(VEurekaClientConstants.RES_INSTANCE_STATUS_KEY) : Lists.newArrayList();
                    Integer rawStatusCode = !CollectionUtils.isEmpty(list) && NumberUtils.isDigits(list.get(0)) ? Integer.valueOf(list.get(0)) : VServerWebExchangeUtils.getRawStatusCode(exchange);
                    final boolean isServerClosing = VDefinedHttpStatus.SERVER_CLOSING_HTTP_STATUS.getStatus()
                        .equals(rawStatusCode);
                    HttpStatus statusCode = isServerClosing ? retryConfig.getStatuses().get(0) : response.getStatusCode();
                    if (isServerClosing) {
                        if (VLogs.isTrace(log)) {  // trace
                            log.info("v - retry rawStatus:{}, httpStatus: {}", rawStatusCode, statusCode);
                        }
                    }

                    boolean retryableStatusCode = retryConfig.getStatuses()
                        .contains(statusCode);

                    // null status code might mean a network exception?
                    if (!retryableStatusCode && statusCode != null) {
                        // try the series
                        retryableStatusCode = retryConfig.getSeries().stream()
                            .anyMatch(series -> statusCode.series().equals(series));
                    }

                    final boolean finalRetryableStatusCode = retryableStatusCode;
                    trace("retryableStatusCode: %b, statusCode %s, configured statuses %s, configured series %s",
                        () -> finalRetryableStatusCode, () -> statusCode,
                        retryConfig::getStatuses, retryConfig::getSeries);

                    HttpMethod httpMethod = exchange.getRequest().getMethod();
                    boolean retryableMethod = retryConfig.getMethods().contains(httpMethod);

                    trace("retryableMethod: %b, httpMethod %s, configured methods %s",
                        () -> retryableMethod, () -> httpMethod, retryConfig::getMethods);
                    return retryableMethod && finalRetryableStatusCode;
                };

                statusCodeRepeat = Repeat.onlyIf(repeatPredicate)
                    .doOnRepeat(context -> reset(context.applicationContext()));

                BackoffConfig backoff = retryConfig.getBackoff();
                if (backoff != null) {
                    statusCodeRepeat = statusCodeRepeat.backoff(getBackoff(backoff));
                }
            }

            // TODO: support timeout, backoff, jitter, etc... in Builder

            Retry<ServerWebExchange> exceptionRetry = null;
            if (!retryConfig.getExceptions().isEmpty()) {
                Predicate<RetryContext<ServerWebExchange>> retryContextPredicate = context -> {

                    ServerWebExchange exchange = context.applicationContext();

                    if (exceedsMaxIterations(exchange, retryConfig)) {
                        return false;
                    }

                    Throwable exception = context.exception();
                    for (Class<? extends Throwable> retryableClass : retryConfig
                        .getExceptions()) {
                        if (retryableClass.isInstance(exception) || (exception != null
                            && retryableClass.isInstance(exception.getCause()))) {
                            trace("exception or its cause is retryable %s, configured exceptions %s",
                                () -> getExceptionNameWithCause(exception),
                                retryConfig::getExceptions);

                            HttpMethod httpMethod = exchange.getRequest().getMethod();
                            boolean retryableMethod = retryConfig.getMethods()
                                .contains(httpMethod);
                            trace("retryableMethod: %b, httpMethod %s, configured methods %s",
                                () -> retryableMethod, () -> httpMethod,
                                retryConfig::getMethods);
                            return retryableMethod;
                        }
                    }
                    trace("exception or its cause is not retryable %s, configured exceptions %s",
                        () -> getExceptionNameWithCause(exception),
                        retryConfig::getExceptions);
                    return false;
                };
                exceptionRetry = Retry.onlyIf(retryContextPredicate)
                    .doOnRetry(context -> reset(context.applicationContext()))
                    .retryMax(retryConfig.getRetries());
                BackoffConfig backoff = retryConfig.getBackoff();
                if (backoff != null) {
                    exceptionRetry = exceptionRetry.backoff(getBackoff(backoff));
                }
            }

            GatewayFilter gatewayFilter = apply(retryConfig.getRouteId(), statusCodeRepeat,
                exceptionRetry);
            return new GatewayFilter() {
                @Override
                public Mono<Void> filter(ServerWebExchange exchange,
                    GatewayFilterChain chain) {
                    return gatewayFilter.filter(exchange, chain);
                }

                @Override
                public String toString() {
                    return filterToStringCreator(VRetryGatewayFilterFactory.this)
                        .append("retries", retryConfig.getRetries())
                        .append("series", retryConfig.getSeries())
                        .append("statuses", retryConfig.getStatuses())
                        .append("methods", retryConfig.getMethods())
                        .append("exceptions", retryConfig.getExceptions()).toString();
                }
            };
        } catch (Exception e) {
            log.error("v-retry create excpetion, default RetryGatewayFilterFactory. ", e);
            return retryGatewayFilterFactory.apply(retryConfig);
        }
    }

    private String getExceptionNameWithCause(Throwable exception) {
        if (exception != null) {
            StringBuilder builder = new StringBuilder(exception.getClass().getName());
            Throwable cause = exception.getCause();
            if (cause != null) {
                builder.append("{cause=").append(cause.getClass().getName()).append("}");
            }
            return builder.toString();
        }
        else {
            return "null";
        }
    }

    private Backoff getBackoff(RetryGatewayFilterFactory.BackoffConfig backoff) {
        return Backoff.exponential(backoff.getFirstBackoff(), backoff.getMaxBackoff(),
            backoff.getFactor(), backoff.isBasedOnPreviousValue());
    }

    public boolean exceedsMaxIterations(ServerWebExchange exchange,
        RetryGatewayFilterFactory.RetryConfig retryConfig) {
        Integer iteration = exchange.getAttribute(RETRY_ITERATION_KEY);

        // TODO: deal with null iteration
        boolean exceeds = iteration != null && iteration >= retryConfig.getRetries();
        trace("exceedsMaxIterations %b, iteration %d, configured retries %d",
            () -> exceeds, () -> iteration, retryConfig::getRetries);
        return exceeds;
    }

    @Deprecated
    /**
     * Use {@link ServerWebExchangeUtils#reset(ServerWebExchange)}
     */
    public void reset(ServerWebExchange exchange) {
        Connection conn = exchange
            .getAttribute(ServerWebExchangeUtils.CLIENT_RESPONSE_CONN_ATTR);
        if (conn != null) {
            trace("disposing response connection before next iteration");
            conn.dispose();
            exchange.getAttributes()
                .remove(ServerWebExchangeUtils.CLIENT_RESPONSE_CONN_ATTR);
        }
        ServerWebExchangeUtils.reset(exchange);
    }

    @Deprecated
    public GatewayFilter apply(Repeat<ServerWebExchange> repeat,
        Retry<ServerWebExchange> retry) {
        return apply(null, repeat, retry);
    }

    public GatewayFilter apply(String routeId, Repeat<ServerWebExchange> repeat,
        Retry<ServerWebExchange> retry) {
        if (routeId != null && getPublisher() != null) {
            // send an event to enable caching
            getPublisher().publishEvent(new EnableBodyCachingEvent(this, routeId));
        }
        return (exchange, chain) -> {
            trace("Entering retry-filter");

            // chain.filter returns a Mono<Void>
            Publisher<Void> publisher = chain.filter(exchange)
                // .log("retry-filter", Level.INFO)
                .doOnSuccess(aVoid -> updateIteration(exchange))
                .doOnError(throwable -> updateIteration(exchange));

            if (retry != null) {
                // retryWhen returns a Mono<Void>
                // retry needs to go before repeat
                publisher = ((Mono<Void>) publisher)
                    .retryWhen((reactor.util.retry.Retry) retry.withApplicationContext(exchange));
            }
            if (repeat != null) {
                // repeatWhen returns a Flux<Void>
                // so this needs to be last and the variable a Publisher<Void>
                publisher = ((Mono<Void>) publisher)
                    .repeatWhen(repeat.withApplicationContext(exchange));
            }

            return Mono.fromDirect(publisher);
        };
    }

    private void updateIteration(ServerWebExchange exchange) {
        int iteration = exchange.getAttributeOrDefault(RETRY_ITERATION_KEY, -1);
        int newIteration = iteration + 1;
        trace("setting new iteration in attr %d", () -> newIteration);
        exchange.getAttributes().put(RETRY_ITERATION_KEY, newIteration);
    }

    @SafeVarargs
    private final void trace(String message, Supplier<Object>... argSuppliers) {
        if (log.isTraceEnabled()) {
            Object[] args = new Object[argSuppliers.length];
            int i = 0;
            for (Supplier<Object> a : argSuppliers) {
                args[i] = a.get();
                ++i;
            }
            log.trace(String.format(message, args));
        }
    }
}
