package com.iqiyi.v.springframwork.cloud.netflix.eureka.serviceregistry.webflux.shutdown;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.PathContainer;
import org.springframework.lang.Nullable;
import org.springframework.util.ObjectUtils;
import org.springframework.util.PathMatcher;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import org.springframework.web.util.UrlPathHelper;
import org.springframework.web.util.pattern.PathPattern;
import org.springframework.web.util.pattern.PathPatternParser;
import org.springframework.web.util.pattern.PatternParseException;
import reactor.core.publisher.Mono;

import java.util.Arrays;

import com.iqiyi.v.springframwork.cloud.client.constants.VEurekaClientConstants;
import com.iqiyi.v.springframwork.cloud.netflix.eureka.serviceregistry.webflux.AppInfoManager;

/**
 * <AUTHOR>
 * @date 2023/5/16 14:27
 */
@Order(Ordered.HIGHEST_PRECEDENCE)
public class GracefulShutDownWebFilter implements WebFilter {

    private static final Logger LOGGER = LoggerFactory.getLogger(GracefulShutDownWebFilter.class);

    private final PatternAdapter[] includePatterns;

    private final PatternAdapter[] excludePatterns;

    public GracefulShutDownWebFilter(String[] includePathPatterns, String[] excludePathPatterns) {
        this.includePatterns = PatternAdapter.initPatterns(includePathPatterns, null);
        this.excludePatterns = PatternAdapter.initPatterns(excludePathPatterns, null);
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        setHttpHeaderIfStopping(exchange);
        return chain.filter(exchange);
    }

    private void setHttpHeaderIfStopping(ServerWebExchange exchange) {
        if (AppInfoManager.getInstance().isStopping() && matches(exchange)) {
            LOGGER.debug("server is stopping");
            final HttpHeaders httpHeaders = exchange.getResponse().getHeaders();
            httpHeaders.set(VEurekaClientConstants.RES_INSTANCE_STATUS_KEY, VEurekaClientConstants.RES_INSTANCE_STATUS_VALUE);
        }
    }

    private boolean matches(ServerWebExchange exchange) {
        final PathContainer pathContainer = exchange.getRequest().getPath().pathWithinApplication();
        if (excludePatterns != null) {
            boolean result = Arrays.stream(excludePatterns).anyMatch(patternAdapter -> patternAdapter.match(pathContainer, true, null));
            if (result) {
                return false;
            }
        }
        if (includePatterns != null) {
            return Arrays.stream(includePatterns).anyMatch(patternAdapter -> patternAdapter.match(pathContainer, true, null));
        }
        return false;
    }

    private static class PatternAdapter {

        private final String patternString;

        @Nullable
        private final PathPattern pathPattern;


        public PatternAdapter(String pattern, @Nullable PathPatternParser parser) {
            this.patternString = pattern;
            this.pathPattern = initPathPattern(pattern, parser);
        }

        @Nullable
        private static PathPattern initPathPattern(String pattern, @Nullable PathPatternParser parser) {
            try {
                return (parser != null ? parser : PathPatternParser.defaultInstance).parse(pattern);
            } catch (PatternParseException ex) {
                return null;
            }
        }

        @Nullable
        public static PatternAdapter[] initPatterns(
            @Nullable String[] patterns, @Nullable PathPatternParser parser) {

            if (ObjectUtils.isEmpty(patterns)) {
                return null;
            }
            return Arrays.stream(patterns)
                .map(pattern -> new PatternAdapter(pattern, parser))
                .toArray(PatternAdapter[]::new);
        }

        public boolean match(Object path, boolean isPathContainer, PathMatcher pathMatcher) {
            if (isPathContainer) {
                PathContainer pathContainer = (PathContainer) path;
                if (this.pathPattern != null) {
                    return this.pathPattern.matches(pathContainer);
                }
                String lookupPath = pathContainer.value();
                path = UrlPathHelper.defaultInstance.removeSemicolonContent(lookupPath);
            }
            return pathMatcher.match(this.patternString, (String) path);
        }
    }
}
