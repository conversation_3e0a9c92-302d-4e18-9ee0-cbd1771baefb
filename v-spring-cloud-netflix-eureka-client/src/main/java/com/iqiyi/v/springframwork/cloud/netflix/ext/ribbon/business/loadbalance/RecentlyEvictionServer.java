package com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.loadbalance;

import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.utils.VEvictionServers;
import lombok.Data;
import org.springframework.cloud.client.ServiceInstance;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/5/20 19:43
 */
@Data
public class RecentlyEvictionServer {
    private long changeTime;
    private ServiceInstance serviceInstance;

    public RecentlyEvictionServer(long changeTime, ServiceInstance serviceInstance) {
        this.changeTime = changeTime;
        this.serviceInstance = serviceInstance;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o instanceof RecentlyEvictionServer) {
            if (serviceInstance == null && ((RecentlyEvictionServer)o).getServiceInstance() == null) {
                return true;
            } else if (serviceInstance == null || ((RecentlyEvictionServer)o).getServiceInstance() == null) {
                return false;
            }
            ServiceInstance o1 = ((RecentlyEvictionServer)o).getServiceInstance();
            return serviceInstance.getHost().equals(o1.getHost()) && serviceInstance.getPort() == o1.getPort();
        }
        return false;
    }

    @Override
    public int hashCode() {
        return serviceInstance == null ? Objects.hash(changeTime, serviceInstance) : serviceInstance.hashCode();
    }

    public String getServId() {
        return VEvictionServers.getServiceInstanceId(this.serviceInstance);
    }
}
