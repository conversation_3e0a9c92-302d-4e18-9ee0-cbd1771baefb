package com.iqiyi.v.springframwork.cloud.netflix.eureka.serviceregistry.webflux.shutdown;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.client.serviceregistry.AutoServiceRegistration;
import org.springframework.cloud.netflix.eureka.serviceregistry.EurekaAutoServiceRegistration;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.List;

import com.iqiyi.v.springframwork.cloud.client.serviceregistry.ServiceGracefulShutDownProperties;
import com.iqiyi.v.springframwork.cloud.netflix.eureka.serviceregistry.webflux.AppInfoManager;


/**
 * <AUTHOR>
 * @date 2023/5/23 18:20
 */
public class ShutDownEventListener implements ApplicationListener<ApplicationEvent> {

    private static final Logger LOGGER = LoggerFactory.getLogger(ShutDownEventListener.class);

    private final List<AutoServiceRegistration> autoServiceRegistrationList;

    private final ServiceGracefulShutDownProperties serviceGracefulShutDownProperties;

    private final ApplicationContext context;

    public ShutDownEventListener(List<AutoServiceRegistration> autoServiceRegistrationList,
        ServiceGracefulShutDownProperties serviceGracefulShutDownProperties, ApplicationContext context) {
        this.autoServiceRegistrationList = autoServiceRegistrationList;
        this.serviceGracefulShutDownProperties = serviceGracefulShutDownProperties;
        this.context = context;
    }

    @Override
    public void onApplicationEvent(ApplicationEvent event) {
        if (event instanceof ContextClosedEvent && ((ContextClosedEvent)event).getApplicationContext() == context) {
            AppInfoManager.getInstance().setStopping(true);
            if (serviceGracefulShutDownProperties.isEnabled()) {
                doGracefulShutdown();
            }
        }
    }

    private void doGracefulShutdown() {
        LOGGER.info("Start to graceful shutdown!");
        try {
            stopServiceRegistration();
            int waitTimeoutMillis = serviceGracefulShutDownProperties.getWaitTimeoutMillis();
            LOGGER.info("Try to sleep {} ms", waitTimeoutMillis);
            Mono.delay(Duration.ofMillis(waitTimeoutMillis))
                .doOnSuccess(aVoid -> LOGGER.info("Ending graceful shutdown!")).block();
        } catch (Exception t) {
            LOGGER.warn("Graceful shutdown error.", t);
        }
    }

    private void stopServiceRegistration() {
        try {
            LOGGER.info("Try to stop auto service registration.");
            for (AutoServiceRegistration autoServiceRegistration : autoServiceRegistrationList) {
                if (autoServiceRegistration instanceof EurekaAutoServiceRegistration) {
                    LOGGER.info("Try to stop eureka auto service registration.");
                    ((EurekaAutoServiceRegistration)autoServiceRegistration).stop();
                }
            }
        } catch (Exception t) {
            LOGGER.warn("Stopping auto service registration error.", t);
        }
    }
}
