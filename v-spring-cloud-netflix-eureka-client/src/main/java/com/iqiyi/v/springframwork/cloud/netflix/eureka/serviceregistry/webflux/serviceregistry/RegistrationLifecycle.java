package com.iqiyi.v.springframwork.cloud.netflix.eureka.serviceregistry.webflux.serviceregistry;

import org.springframework.cloud.client.serviceregistry.Registration;
import org.springframework.core.Ordered;

/**
 * <AUTHOR>
 */
public interface RegistrationLifecycle extends Ordered {
    int DEFAULT_ORDER = 0;

    /**
     * 注册前操作
     * 
     * @param registration
     *            Registration
     */
    default void preRegister(Registration registration) {}

    /**
     * 注册后操作
     * 
     * @param registration
     *            Registration
     */
    default void postRegister(Registration registration) {}

    /**
     * Get the order value of this object.
     * <p>
     * Higher values are interpreted as lower priority. As a consequence, the object with the lowest value has the
     * highest priority (somewhat analogous to Servlet {@code load-on-startup} values).
     * <p>
     * Same order values will result in arbitrary sort positions for the affected objects.
     * 
     * @return the order value
     * @see #HIGHEST_PRECEDENCE
     * @see #LOWEST_PRECEDENCE
     */
    @Override
    default int getOrder() {
        return DEFAULT_ORDER;
    }
}
