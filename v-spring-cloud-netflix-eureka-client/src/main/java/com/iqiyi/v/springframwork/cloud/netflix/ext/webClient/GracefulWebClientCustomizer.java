package com.iqiyi.v.springframwork.cloud.netflix.ext.webClient;


import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.reactive.function.client.WebClientCustomizer;
import org.springframework.cloud.loadbalancer.support.LoadBalancerClientFactory;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.WebClient;

import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.p.VLoadBalanceProperties;

@Slf4j
public class GracefulWebClientCustomizer implements WebClientCustomizer {

    private VLoadBalanceProperties vLoadBalanceProperties;

    private LoadBalancerClientFactory loadBalancerClientFactory;

    public GracefulWebClientCustomizer(VLoadBalanceProperties vLoadBalanceProperties, LoadBalancerClientFactory loadBalancerClientFactory) {
        this.vLoadBalanceProperties = vLoadBalanceProperties;
        this.loadBalancerClientFactory = loadBalancerClientFactory;
    }

    @Override
    public void customize(WebClient.Builder webClientBuilder) {
        webClientBuilder.filters((filterFunctions) -> {
            for (ExchangeFilterFunction filterFunction : filterFunctions) {
                if (filterFunction instanceof VResponseStatusUpdateUpServerListExchangeFilterFunction) {
                    return;
                }
            }
            filterFunctions.add(new VResponseStatusUpdateUpServerListExchangeFilterFunction(loadBalancerClientFactory, vLoadBalanceProperties));
        });
        log.info("WebClient已支持可靠负载");
    }
}