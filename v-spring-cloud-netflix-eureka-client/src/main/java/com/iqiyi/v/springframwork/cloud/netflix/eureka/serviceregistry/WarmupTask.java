package com.iqiyi.v.springframwork.cloud.netflix.eureka.serviceregistry;

import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;
import java.util.concurrent.Callable;

import com.iqiyi.v.springframwork.cloud.client.gracefulstart.GracefulStartInfoManager;
import lombok.extern.slf4j.Slf4j;

import org.springframework.util.CollectionUtils;

/**
 * 预热任务
 */
@Slf4j
public class WarmupTask implements Callable {

	private List<WarmUp> warmUps;

	WarmupTask(List<WarmUp> warmUps) {
		this.warmUps = warmUps;
	}

	@Override
	public Object call() throws Exception {
		log.info("WarmupTask start call");
		List<String> pathList = GracefulStartInfoManager.getInstance().getPathList();
		String host = GracefulStartInfoManager.getInstance().getHost();
		int port = GracefulStartInfoManager.getInstance().getPort();
		int executeWarmUpLogicTimes = GracefulStartInfoManager.getInstance().getExecuteWarmUpLogicTimes();
		long start = System.currentTimeMillis();
        log.info(executeWarmUpLogicTimes > 0 ? "优雅上线预热逻辑开始执行。" : "未执行优雅上下线预热逻辑。");
		for (int i = 0; i < executeWarmUpLogicTimes; i++) {
			if (!CollectionUtils.isEmpty(pathList)) {
				for (String path : pathList) {
					String urlStr = "http://" + host + ":" + port + "/" + path;
					URL url = new URL(urlStr);
					HttpURLConnection http = (HttpURLConnection) url.openConnection();
					http.disconnect();
					log.info("url:{},resCode:{},curl cost:{}", urlStr, http.getResponseCode(), System
							.currentTimeMillis() - start);
				}

			}
			if (CollectionUtils.isEmpty(warmUps)) {
				new DefaultWarmUpImpl().warmUp();
				return true;
			}
			for (WarmUp warmUp : warmUps) {
				warmUp.warmUp();
			}
		}
		return true;
	}
}
