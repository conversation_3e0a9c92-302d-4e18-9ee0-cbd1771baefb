package com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.p;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.ArrayList;
import java.util.List;

// 暂保留，兼容老配置
@Data
@Deprecated
@ConfigurationProperties(prefix = "v-ribbon")
public class VRibbonProperties  {
    /**
     * 立即加载
     */
    protected List<String> serviceIdList = new ArrayList<>();
}
