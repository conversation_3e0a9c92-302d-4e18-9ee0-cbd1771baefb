package com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business;

import com.google.common.collect.Lists;
import com.iqiyi.v.springframwork.cloud.client.constants.VEurekaClientConstants;
import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.p.VLoadBalanceProperties;
import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.utils.VEvictionServers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.loadbalancer.reactive.ReactiveLoadBalancer;
import org.springframework.cloud.loadbalancer.support.LoadBalancerClientFactory;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.List;

@Slf4j
public class VRibbonExceptionServerInterceptor implements ClientHttpRequestInterceptor {

    private LoadBalancerClientFactory loadBalancerClientFactory;
    private VLoadBalanceProperties vLoadBalanceProperties;

    public VRibbonExceptionServerInterceptor(LoadBalancerClientFactory loadBalancerClientFactory, VLoadBalanceProperties vLoadBalanceProperties) {
        this.loadBalancerClientFactory = loadBalancerClientFactory;
        this.vLoadBalanceProperties = vLoadBalanceProperties;
    }


    @Override
    public ClientHttpResponse intercept(final HttpRequest request, final byte[] body,
                                        final ClientHttpRequestExecution execution) throws IOException {
        try {
            final ClientHttpResponse execute = execution.execute(request, body);
            ServiceInstance serviceInstance = VRibbonHttpContext.getServiceInstance(request);
            List<String> list = execute.getHeaders() != null && execute.getHeaders().get(VEurekaClientConstants.RES_INSTANCE_STATUS_KEY) != null ? execute.getHeaders().get(VEurekaClientConstants.RES_INSTANCE_STATUS_KEY) : Lists.newArrayList();
            int rawStatusCode = !CollectionUtils.isEmpty(list) && NumberUtils.isDigits(list.get(0)) ? Integer.valueOf(list.get(0)) : execute.getRawStatusCode();
            if (serviceInstance != null && !StringUtils.isEmpty(serviceInstance.getServiceId()) &&
                execute != null && VEvictionServers.isEviction((rawStatusCode))) {
                String serviceId = serviceInstance.getServiceId();
                ReactiveLoadBalancer<ServiceInstance> loadBalancer = loadBalancerClientFactory.getInstance(serviceId);
                VEvictionServers.evictionServ(serviceInstance.getServiceId(), serviceInstance.getHost(), serviceInstance.getPort(), serviceInstance.getUri(),
                    rawStatusCode, loadBalancer, vLoadBalanceProperties);
            }
            return execute;
        } catch (Exception e) {
//            ServiceInstance serviceInstance = VRibbonHttpContext.getServiceInstance(request);
//            if (serviceInstance == null || StringUtils.isEmpty(serviceInstance.getServiceId())) {
//                throw e;
//            }
//            String serviceId = serviceInstance.getServiceId();
//            RibbonLoadBalancerContext loadBalancerContext = springClientFactory.getLoadBalancerContext(serviceId);
//            PingUrl pingUrl = new PingUrl();
//            Server s = new Server(serviceInstance.getHost(), serviceInstance.getPort());
//            if (!pingUrl.isAlive(s)) {
//                loadBalancerContext.getLoadBalancer().markServerDown(s);
//            }
            throw e;
        }

    }
}