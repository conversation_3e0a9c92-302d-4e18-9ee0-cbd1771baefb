package com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.p;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.ArrayList;
import java.util.List;

@Data
@ConfigurationProperties(prefix = "v-load.balance.props")
public class VLoadBalanceProperties {

    private boolean enable = true;

    /**
     * 可靠负载满足大于 leastServerCount 实例，才开启
     */
    private int leastServerCount = 3;

    /**
     * 自我保护比率。例：10个实例的Server。当可用Server = 6的时候，不可以再剔除。
     */
    private double selfPreservationRate = 0.6D;

    /**
     * 已排除实例作用时间，= 32 表示 32s 后才会继续使用。<br/>
     * 有一种情况是，可用实例排除了已经关闭的服务器，但是可能在下一秒又从全部实例（全部实例不能及时感知到实例下线）中同步回来且还是不可用。<br/>
     * 这样我们就可以维护一个列表，当拿到不可用的实例再换一个，降低一个不可用的远程调用。
     */
    private int retentionTimeInMsInEvictionQueue = 32 * 1000;

    /**
     * 立即加载
     */
    protected List<String> serviceIdList = new ArrayList<>();

    /**
     * 重试状态码，多个以逗号(,)分隔
     */
    private String retryableStatusCodes = "600";

    private int pingIntervalSeconds = 10;

    public boolean isEnable(int allServerCount, int upServerCount) {
        int tmpSelfServerCount =  (int) (allServerCount * selfPreservationRate);
        return enable
            && allServerCount > this.leastServerCount
            && upServerCount > tmpSelfServerCount;
    }
}