package com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.loadbalance;

import com.google.gson.Gson;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

import com.iqiyi.solar.config.client.CloudConfig;
import com.iqiyi.solar.config.client.CloudConfigChange;
import com.iqiyi.solar.config.client.CloudConfigService;
import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.loadbalance.LoadBalanceConfig.Config;
import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.loadbalance.LoadBalanceConfig.ZoneLoadBalanceConfig;

/**
 * <AUTHOR>
 * @date 2023/11/3 18:15
 */

public class LoadBalanceConfigServiceImpl implements LoadBalanceConfigService {

    private static final Log LOG = LogFactory.getLog(LoadBalanceConfigServiceImpl.class);

    private final ConcurrentHashMap<String, LoadBalanceConfig> loadBalanceConfigMap = new ConcurrentHashMap<>();

    private final CloudConfig cloudConfig;

    private static final Gson GSON = new Gson();

    private final Map<String, LoadBalanceConfigChangeListener> loadBalanceConfigChangeListenerMap = new ConcurrentHashMap<>();

    public LoadBalanceConfigServiceImpl() {
        cloudConfig = CloudConfigService.builder().withAppID("vip-eureka").withEnv("pro").build();
        cloudConfig.addChangeListener(changeEvent -> {
            for (String key : changeEvent.changedKeys()) {
                CloudConfigChange change = changeEvent.getChange(key);
                if (loadBalanceConfigMap.containsKey(key)) {
                    try {
                        LoadBalanceConfig newLoadBalanceConfig;
                        if (change.getNewValue() != null) {
                            ZoneLoadBalanceConfig[] zoneLoadBalanceConfigs =
                                GSON.fromJson(change.getNewValue(), ZoneLoadBalanceConfig[].class);
                            newLoadBalanceConfig = new LoadBalanceConfig(key, Arrays.asList(zoneLoadBalanceConfigs));
                        } else {
                            newLoadBalanceConfig = new LoadBalanceConfig(key, Collections.emptyList());
                        }
                        loadBalanceConfigMap.put(key, newLoadBalanceConfig);
                        LoadBalanceConfigChangeListener loadBalanceConfigChangeListener = loadBalanceConfigChangeListenerMap.get(key);
                        if (loadBalanceConfigChangeListener != null) {
                            loadBalanceConfigChangeListener.loadBalanceConfigChange(newLoadBalanceConfig);
                        }
                        LOG.info(String.format("更新最新配置成功，key = %s, old value = %s, new value = %s", key, change.getOldValue(), change.getNewValue()));
                    } catch (Exception e) {
                        LOG.error(String.format("更新最新配置失败，key = %s, old value = %s, new value = %s", key, change.getOldValue(), change.getNewValue()), e);
                    }
                }
            }
        });
    }

    @Override
    public Optional<LoadBalanceConfig> query(String serviceName) {
        String serviceNameUpper = serviceName.toUpperCase();
        if (loadBalanceConfigMap.containsKey(serviceNameUpper)) {
            return Optional.of(loadBalanceConfigMap.get(serviceNameUpper));
        }
        String loadBalanceConfigJson = cloudConfig.getProperty(serviceNameUpper, null);
        if (loadBalanceConfigJson == null) {
            return Optional.empty();
        } else {
            try {
                ZoneLoadBalanceConfig[] zoneLoadBalanceConfigs = GSON.fromJson(loadBalanceConfigJson, ZoneLoadBalanceConfig[].class);
                LoadBalanceConfig loadBalanceConfig = new LoadBalanceConfig(serviceNameUpper, Arrays.asList(zoneLoadBalanceConfigs));
                loadBalanceConfigMap.put(serviceNameUpper, loadBalanceConfig);
                return Optional.of(loadBalanceConfig);
            } catch (Exception e) {
                LOG.error("解析路由策略配置失败, serviceName=" + serviceName, e);
                return Optional.empty();
            }
        }
    }

    @Override
    public List<Config> query(String serviceName, String zone) {
        List<Config> configs = new ArrayList<>();
        Optional<LoadBalanceConfig> loadBalanceConfigOptional = query(serviceName);
        if (loadBalanceConfigOptional.isPresent()) {
            List<ZoneLoadBalanceConfig> zoneLoadBalanceConfigs = loadBalanceConfigOptional.get().getZoneLoadBalanceConfigs();
            Optional<ZoneLoadBalanceConfig> zoneLoadBalanceConfigOptional = zoneLoadBalanceConfigs.stream()
                .filter(config -> zone.equalsIgnoreCase(config.getZone()))
                .findFirst();
            zoneLoadBalanceConfigOptional.ifPresent(zoneLoadBalanceConfig -> {
                configs.addAll(zoneLoadBalanceConfig.getConfig());
                Optional<Config> normalConfigOptional = configs.stream().filter(config -> !config.isBackup()).findFirst();
                if (!normalConfigOptional.isPresent()) {
                    configs.add(new Config(zone));
                }
            });
        }
        if (configs.isEmpty()) {
            // 不配置默认走同zone优先调用
            configs.add(new Config(zone));
        }
        return configs;
    }

    @Override
    public void addLoadBalanceConfigChangeListener(String serviceName, LoadBalanceConfigChangeListener loadBalanceConfigChangeListener) {
        loadBalanceConfigChangeListenerMap.put(serviceName.toUpperCase(), loadBalanceConfigChangeListener);
    }

}


