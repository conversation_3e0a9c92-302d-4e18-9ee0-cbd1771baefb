package com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.loadbalance;

import com.netflix.appinfo.InstanceInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.loadbalancer.config.LoadBalancerZoneConfig;
import org.springframework.cloud.loadbalancer.core.DelegatingServiceInstanceListSupplier;
import org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier;
import org.springframework.cloud.netflix.eureka.EurekaServiceInstance;
import org.springframework.context.ApplicationContext;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Timer;
import java.util.TimerTask;
import java.util.UUID;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.stream.Collectors;

import com.iqiyi.v.springframwork.cloud.client.constants.VEurekaClientConstants;
import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.loadbalance.SmoothWeightedRoundRobin.WeightedServer;
import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.p.VLoadBalanceProperties;
import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.utils.VEvictionServers;
import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.utils.VLogs;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/31 14:29
 */
@Slf4j
public class VReachableServiceInstanceListSupplier extends DelegatingServiceInstanceListSupplier {

    private static final String ZONE = "zone";

    private static final String DEFAULT_ZONE = "UNKNOWN";

    private volatile boolean close = false;
    Thread reachableServersEvictionTaskThread = null; // 定时更新可用注册表
    private Timer recentlyEvictionServersTimer = null; // 正在下线机器定时更新
    protected Timer lbTimer = null; // 注册表，全部 -> 可用定时更新
    protected String name = "default";
    List<ServiceInstance> reachableServiceInstances; // 引用可用注册表

    /**
     * 最近剔除Serv队列
     */
    private BlockingQueue<RecentlyEvictionServer> recentlyEvictionServersQueue = new LinkedBlockingQueue<>(1024);
    /**
     * 可用注册表剔除不可用Serv队列
     */
    private BlockingQueue<RecentlyEvictionServer> reachableServersEvictionQueue = new LinkedBlockingQueue<>(1024);

    protected ReadWriteLock allServerLock = new ReentrantReadWriteLock();
    protected ReadWriteLock upServerLock = new ReentrantReadWriteLock();

    private VLoadBalanceProperties vLoadBalanceProperties;
    protected AtomicBoolean pingInProgress = new AtomicBoolean(false);

    private volatile SmoothWeightedRoundRobin<ServiceInstance> smoothWeightedRoundRobin;
    
    private ApplicationContext applicationContext;

    public VReachableServiceInstanceListSupplier(ServiceInstanceListSupplier delegate) {
        super(delegate);
    }

    public VReachableServiceInstanceListSupplier(ServiceInstanceListSupplier delegate,
        VLoadBalanceProperties vLoadBalanceProperties, ApplicationContext applicationContext) {
        this(delegate);
        this.vLoadBalanceProperties = vLoadBalanceProperties;
        this.applicationContext = applicationContext;
        // 剔除队列任务&可用注册表变更消息订阅
        this.initRecentlyEvictionServerTask();
        log.info("已支持可靠负载。");
    }

    public void initRecentlyEvictionServerTask() {
        lbTimer = new Timer("NFLoadBalancer-PingTimer-" + name,
                true);
        lbTimer.schedule(new PingTask(), 0, vLoadBalanceProperties.getPingIntervalSeconds() * 1000);
        try {
            new Pinger(new VSerialPingStrategy()).runPinger();
        } catch (Exception e) {}

        // 最近下线Serv队列定时维护
        recentlyEvictionServersTimer = new Timer("Timer-RecentlyEvictionServersTimer-" + name, true);
        recentlyEvictionServersTimer.schedule(new EvictionServerTimerTask(vLoadBalanceProperties), 0,
                vLoadBalanceProperties.getRetentionTimeInMsInEvictionQueue());
        // 可用注册表更新
        reachableServersEvictionTaskThread = new Thread(new ReachableServersEvictionRunnable(), "ReachableServersEvictionTask-" + name);
        reachableServersEvictionTaskThread.setDaemon(true);
        reachableServersEvictionTaskThread.start();
    }

    private List<WeightedServer<ServiceInstance>> constructWeightedServers(List<ServiceInstance> serviceInstances) {
        return serviceInstances.stream().map(serviceInstance -> new WeightedServer<>(serviceInstance,
            getWeight(serviceInstance), getStartTimestamp(serviceInstance), getWarmUpTime(serviceInstance)))
            .collect(Collectors.toList());
    }

    private int getWarmUpTime(ServiceInstance serviceInstance) {
        String flowWarmUpTimeStr = serviceInstance.getMetadata().get(VEurekaClientConstants.FLOW_WARM_UP_TIME_SECONDS_KEY);
        return flowWarmUpTimeStr != null ? Integer.parseInt(flowWarmUpTimeStr) : 0;
    }

    private long getStartTimestamp(ServiceInstance serviceInstance) {
        String startTimestampStr = serviceInstance.getMetadata().get(VEurekaClientConstants.START_TIMESTAMP_KEY);
        return startTimestampStr != null ? Long.parseLong(startTimestampStr) : 0;
    }

    private int getWeight(ServiceInstance serviceInstance) {
        String weightStr = serviceInstance.getMetadata().get(VEurekaClientConstants.WEIGHT_KEY);
        return weightStr != null ? Integer.parseInt(weightStr) : VEurekaClientConstants.DEFAULT_WEIGHT;
    }

    class PingTask extends TimerTask {
        public void run() {
            try {
                new Pinger(new VSerialPingStrategy()).runPinger();
            } catch (Exception e) {
                log.error("LoadBalancer [{}]: Error pinging", name, e);
            }
        }
    }

    class VSerialPingStrategy {
        public boolean[] pingServers(ServiceInstance[] servers) {
            int numCandidates = servers.length;
            boolean[] results = new boolean[numCandidates];

            log.debug("LoadBalancer:  PingTask executing [{}] servers configured", numCandidates);

            for (int i = 0; i < numCandidates; i++) {
                results[i] = false; /* Default answer is DEAD. */
                try {
                    // NOTE: IFF we were doing a real ping
                    // assuming we had a large set of servers (say 15)
                    // the logic below will run them serially
                    // hence taking 15 times the amount of time it takes
                    // to ping each server
                    // A better method would be to put this in an executor
                    // pool
                    // But, at the time of this writing, we dont REALLY
                    // use a Real Ping (its mostly in memory eureka call)
                    // hence we can afford to simplify this design and run
                    // this
                    // serially
                    List<ServiceInstance> serviceInstances = Collections.unmodifiableList(recEviSer());
                    final ServiceInstance server = servers[i];
                    results[i] = serviceInstances.isEmpty() ?
                            isAlive(server) : isAlive(server) && !serviceInstances.contains(server);
                    if (VLogs.isTrace(log)) { // trace
                        log.info("VSerialPingStrategy server: {}, status: {}, queue: {}, time: {}",
                                server, results[i], serviceInstances, LocalDateTime.now());
                    }
                } catch (Exception e) {
                    log.error(String.format("Exception while pinging Server: '%s'", servers[i]), e);
                }
            }
            return results;
        }

        public boolean isAlive(ServiceInstance serviceInstance) {
            if (serviceInstance instanceof EurekaServiceInstance) {
                EurekaServiceInstance eurekaServiceInstance = (EurekaServiceInstance) serviceInstance;
                return eurekaServiceInstance != null && eurekaServiceInstance.getInstanceInfo() != null &&
                        eurekaServiceInstance.getInstanceInfo().getStatus() == InstanceInfo.InstanceStatus.UP;
            }
            return true;
        }

        /**
         * 最近剔除队列
         * @return
         */
        public List<ServiceInstance> recEviSer() {
            return recentlyEvictionServersQueue.stream().map(RecentlyEvictionServer::getServiceInstance).collect(Collectors.toList());
        }
    }

    @Override
    public Flux<List<ServiceInstance>> get() {
        Optional<ServiceInstance> instanceOptional;
        LoadBalancerZoneConfig zoneConfig = applicationContext.getBean(LoadBalancerZoneConfig.class);
        if (VEurekaClientConstants.ZONE_PRE.equals(zoneConfig.getZone())) {
            instanceOptional = selectForPre();
        } else {
            instanceOptional = smoothWeightedRoundRobin.select();
        }
        return instanceOptional
                .map(serviceInstance -> Flux
                        .defer(() -> Mono.fromCallable(() -> Collections.singletonList(serviceInstance))))
                .orElseGet(() -> getDelegate().get());
    }

    private Optional<ServiceInstance> selectForPre() {
        List<WeightedServer<ServiceInstance>> weightedServerList = smoothWeightedRoundRobin.getWeightedServerList();
        if (weightedServerList.isEmpty()) {
            return Optional.empty();
        }

        String pressureTestMode = TraceContext.getPressureTestMode();
        List<ServiceInstance> filteredList;
        if (Boolean.TRUE.toString().equalsIgnoreCase(pressureTestMode)
            || Boolean.FALSE.toString().equalsIgnoreCase(pressureTestMode)) {
            // 对于压测流量优先访问服务生产实例
            filteredList = weightedServerList.stream().map(WeightedServer::getServer)
                .filter(server -> !VEurekaClientConstants.ZONE_PRE.equals(getZone(server)))
                .collect(Collectors.toList());
        } else {
            // 对于非压测流量优先访问服务预发实例
            filteredList = weightedServerList.stream().map(WeightedServer::getServer)
                .filter(server -> VEurekaClientConstants.ZONE_PRE.equals(getZone(server))).collect(Collectors.toList());
        }
        return filteredList.isEmpty()
            ? Optional
                .of(weightedServerList.get(ThreadLocalRandom.current().nextInt(weightedServerList.size())).getServer())
            : Optional.of(filteredList.get(ThreadLocalRandom.current().nextInt(filteredList.size())));
    }

    private String getZone(ServiceInstance serviceInstance) {
        String zone = null;
        Map<String, String> metadata = serviceInstance.getMetadata();
        if (metadata != null) {
            zone = metadata.get(ZONE);
        }
        return zone != null ? zone : DEFAULT_ZONE;
    }

    public Flux<List<ServiceInstance>> getAllServiceInstances() {
        return getDelegate().get();
    }

    public List<ServiceInstance> getReachableServiceInstances() {
        return reachableServiceInstances;
    }

    public void addRecentlyEvictionServers(RecentlyEvictionServer recentlyEvictionServer) {
        if (!recentlyEvictionServersQueue.contains(recentlyEvictionServer)) {
            if (VLogs.isTrace(log)) { // trace
                log.info("v eviction instances : {}", recentlyEvictionServer);
            }
            recentlyEvictionServersQueue.add(recentlyEvictionServer);
            reachableServersEvictionQueue.add(recentlyEvictionServer);
        }
    }

    public void shutdown() {
        close = true;
        reachableServersEvictionTaskThread.interrupt();
        recentlyEvictionServersTimer.cancel();
    }

    public void setName(String name) {
        if (StringUtils.isNotBlank(name)) {
            this.name = name;
        }
    }

    /**
     * 最近剔除Sev队列
     */
    class EvictionServerTimerTask extends TimerTask {
        private VLoadBalanceProperties vLoadBalanceProperties;

        public EvictionServerTimerTask(VLoadBalanceProperties vLoadBalanceProperties) {
            this.vLoadBalanceProperties = vLoadBalanceProperties;
        }

        @Override
        public void run() {
            try {
                final UUID uuid = UUID.randomUUID();
                if (VLogs.isTrace(log)) { // trace
                    log.info("uuid: {} - EvictionServerTimerTask start: {}, queue: {}", uuid, LocalDateTime.now(), recentlyEvictionServersQueue);
                }
                final Iterator<RecentlyEvictionServer> iterator = recentlyEvictionServersQueue.iterator();
                while (iterator.hasNext()) {
                    RecentlyEvictionServer recentlyChangeServer = iterator.next();
                    if (recentlyChangeServer != null && (recentlyChangeServer.getChangeTime() + vLoadBalanceProperties.getRetentionTimeInMsInEvictionQueue()) < System.currentTimeMillis()) {
                        iterator.remove();
                        if (VLogs.isTrace(log)) { // trace
                            log.info("uuid: {} - v-ld recently-change-server task. server : {}", uuid, recentlyChangeServer);
                        }
                    } else {
                        break;
                    }
                }
            } catch (Exception e) {
                log.error("v recently eviction servers timer, exception. ", e);
            }
        }
    }
    /**
     * 可用注册表清除Timer
     */
    class ReachableServersEvictionRunnable implements Runnable {

        @Override
        public void run() {
            while (true && !Thread.currentThread().isInterrupted()) {
                Map<String, ServiceInstance> reachableServersMap = new ConcurrentHashMap<>();
                try {

                    RecentlyEvictionServer recentlyEvictionServer = reachableServersEvictionQueue.take();
                    do {
                        reachableServersMap.put(recentlyEvictionServer.getServId(), recentlyEvictionServer.getServiceInstance());
                    } while ((recentlyEvictionServer = reachableServersEvictionQueue.poll()) != null);

                    final ArrayList<ServiceInstance> servers = new ArrayList<>(getReachableServiceInstances());
                    final Iterator<ServiceInstance> iterator = servers.iterator();
                    while (iterator.hasNext()) {
                        final ServiceInstance next = iterator.next();
                        if (reachableServersMap.containsKey(VEvictionServers.getServiceInstanceId(next))) {
                            iterator.remove();
                        }
                    }
                    Lock lock = upServerLock.writeLock();
                    lock.lock();
                    reachableServiceInstances = servers;
                    smoothWeightedRoundRobin = new SmoothWeightedRoundRobin<>(constructWeightedServers(servers));
                    lock.unlock();
                    if (VLogs.isTrace(log)) { // trace
                        final List<ServiceInstance> reachableServers = getReachableServiceInstances();
                        log.info("\n registry-list: {}, \n registry-list-size: {}", reachableServers, reachableServers == null ? -1 : reachableServers.size());
                    }
                } catch (InterruptedException e) {
                    log.error("interrupt : " + Thread.currentThread().isInterrupted());
                    if (close) { // 只有系统关闭了，才恢复标志位
                        Thread.currentThread().interrupt();
                        log.error("interrupt : " + Thread.currentThread().isInterrupted());
                    }
                } catch (Exception e) {
                    log.error("v load balance ReachableServersEvictionRunnable, exception.", e);
                }
            }
            if (VLogs.isTrace(log)) { // trace
                log.info("v-lb reachable servers eviction thread down. thread interrupt : " + Thread.currentThread().isInterrupted());
            }
        }
    }
    class Pinger {

        private final VSerialPingStrategy pingerStrategy;

        public Pinger(VSerialPingStrategy pingerStrategy) {
            this.pingerStrategy = pingerStrategy;
        }

        public void runPinger() throws Exception {
            if (!pingInProgress.compareAndSet(false, true)) {
                return; // Ping in progress - nothing to do
            }

            // we are "in" - we get to Ping

            ServiceInstance[] allServers = null;
            boolean[] results = null;

            Lock allLock = null;
            Lock upLock = null;

            try {
                /*
                 * The readLock should be free unless an addServer operation is
                 * going on...
                 */
                allLock = allServerLock.readLock();
                allLock.lock();
                allServers = getAllServiceInstances().blockFirst().toArray(new ServiceInstance[getAllServiceInstances().blockFirst().size()]);
                allLock.unlock();

                int numCandidates = allServers.length;
                results = pingerStrategy.pingServers(allServers);

                final List<ServiceInstance> newUpList = new ArrayList<>();
                for (int i = 0; i < numCandidates; i++) {
                    boolean isAlive = results[i];
                    ServiceInstance svr = allServers[i];
                    if (isAlive) {
                        newUpList.add(svr);
                    }
                }
                upLock = upServerLock.writeLock();
                upLock.lock();
                if (!equals(reachableServiceInstances, newUpList)) {
                    reachableServiceInstances = newUpList;
                    smoothWeightedRoundRobin = new SmoothWeightedRoundRobin<>(constructWeightedServers(newUpList));
                }
                upLock.unlock();

            } finally {
                pingInProgress.set(false);
            }
        }

        private boolean equals(List<ServiceInstance> originalList, List<ServiceInstance> newList) {
            if (newList == originalList) {
                return true;
            }

            if(originalList == null || newList == null){
                return false;
            }

            Iterator<ServiceInstance> e1 = originalList.iterator();
            Iterator<ServiceInstance> e2 = newList.iterator();
            while (e1.hasNext() && e2.hasNext()) {
                ServiceInstance o1 = e1.next();
                ServiceInstance o2 = e2.next();
                if (!o1.equals(o2) || getWeight(o1) != getWeight(o2)) {
                    return false;
                }
            }
            return !(e1.hasNext() || e2.hasNext());
        }
    }
}
