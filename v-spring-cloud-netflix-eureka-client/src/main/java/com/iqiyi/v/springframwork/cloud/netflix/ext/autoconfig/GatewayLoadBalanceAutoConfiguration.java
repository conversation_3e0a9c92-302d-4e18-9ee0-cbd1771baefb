package com.iqiyi.v.springframwork.cloud.netflix.ext.autoconfig;

import com.iqiyi.v.springframwork.cloud.netflix.ext.gateway.filter.VRetryGatewayFilterFactory;
import com.iqiyi.v.springframwork.cloud.netflix.ext.gateway.filter.global.VResponseStatusUpdateUpServerListFilter;
import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.loadbalance.LoadBalanceConfigService;
import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.loadbalance.LoadBalanceConfigServiceImpl;
import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.p.VLoadBalanceProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.gateway.config.conditional.ConditionalOnEnabledFilter;
import org.springframework.cloud.loadbalancer.annotation.LoadBalancerClients;
import org.springframework.cloud.loadbalancer.support.LoadBalancerClientFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR>
 * @date 2022/5/5 14:40
 */
@Slf4j
@Configuration
@LoadBalancerClients(defaultConfiguration = LoadBalanceConfiguration.class)
@EnableConfigurationProperties({VLoadBalanceProperties.class})
@ConditionalOnClass(name = {"org.springframework.cloud.netflix.ribbon.SpringClientFactory", "org.springframework.cloud.gateway.filter.factory.GatewayFilterFactory"})
@ConditionalOnProperty(name = "v-load.balance.enabled", matchIfMissing = true)
public class GatewayLoadBalanceAutoConfiguration {

    @Bean
    @ConditionalOnClass(name = "org.springframework.cloud.gateway.filter.GlobalFilter")
    public VResponseStatusUpdateUpServerListFilter responseStatusCatchFilter(LoadBalancerClientFactory loadBalancerClientFactory,
                                                                             VLoadBalanceProperties vLoadBalanceProperties) {
        log.info("已支持会员网关可靠负载。");
        return new VResponseStatusUpdateUpServerListFilter(loadBalancerClientFactory, vLoadBalanceProperties);
    }

    @Bean
    @ConditionalOnClass(name = "org.springframework.cloud.gateway.filter.factory.GatewayFilterFactory")
    @ConditionalOnEnabledFilter
    public VRetryGatewayFilterFactory vRetryGatewayFilterFactory() {
        return new VRetryGatewayFilterFactory();
    }

    @Bean
    @ConditionalOnMissingBean
    public LoadBalanceConfigService loadBalanceConfigService() {
        return new LoadBalanceConfigServiceImpl();
    }

}

