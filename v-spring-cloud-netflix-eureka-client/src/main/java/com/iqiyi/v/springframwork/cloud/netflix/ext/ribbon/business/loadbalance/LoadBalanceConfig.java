package com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.loadbalance;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/3 18:14
 */
public class LoadBalanceConfig {

    private final String serviceName;

    private final List<ZoneLoadBalanceConfig> zoneLoadBalanceConfigs;

    public LoadBalanceConfig(String serviceName, List<ZoneLoadBalanceConfig> zoneLoadBalanceConfigs) {
        this.serviceName = serviceName;
        this.zoneLoadBalanceConfigs = zoneLoadBalanceConfigs;
    }

    public List<ZoneLoadBalanceConfig> getZoneLoadBalanceConfigs() {
        return zoneLoadBalanceConfigs;
    }

    public String getServiceName() {
        return serviceName;
    }

    public static class ZoneLoadBalanceConfig {

        private String zone;

        private List<Config> config;

        public String getZone() {
            return zone;
        }

        public void setZone(String zone) {
            this.zone = zone;
        }

        public List<Config> getConfig() {
            return config;
        }

        public void setConfig(List<Config> config) {
            this.config = config;
        }
    }

    public static class Config {

        private String zone;
        private int weight = 1;
        private int minInstances = 1;
        private boolean backup = false;

        public Config() {
        }

        public Config(String zone) {
            this.zone = zone;
        }

        public String getZone() {
            return zone;
        }

        public void setZone(String zone) {
            this.zone = zone;
        }

        public int getWeight() {
            return weight;
        }

        public void setWeight(int weight) {
            this.weight = weight;
        }

        public int getMinInstances() {
            return minInstances;
        }

        public void setMinInstances(int minInstances) {
            this.minInstances = minInstances;
        }

        public boolean isBackup() {
            return backup;
        }

        public void setBackup(boolean backup) {
            this.backup = backup;
        }
    }


}
