package com.iqiyi.v.springframwork.cloud.netflix.ext.autoconfig;

import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.loadbalance.CustomServiceInstanceListSupplier;
import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.loadbalance.LoadBalanceConfigService;
import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.loadbalance.VReachableServiceInstanceListSupplier;
import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.loadbalance.core.VRoundRobinLoadBalancer;
import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.p.VLoadBalanceProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.cloud.loadbalancer.config.LoadBalancerZoneConfig;
import org.springframework.cloud.loadbalancer.core.ReactorLoadBalancer;
import org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier;
import org.springframework.cloud.loadbalancer.support.LoadBalancerClientFactory;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

/**
 * <AUTHOR>
 * @date 2022/5/5 18:57
 */
@Slf4j
@Configuration
public class LoadBalanceConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public ReactorLoadBalancer<ServiceInstance> reactorServiceInstanceLoadBalancer(Environment environment,
                                                                                   LoadBalancerClientFactory loadBalancerClientFactory) {
        String name = environment.getProperty(LoadBalancerClientFactory.PROPERTY_NAME);
        return new VRoundRobinLoadBalancer(
                loadBalancerClientFactory.getLazyProvider(name, ServiceInstanceListSupplier.class), name);
    }

    @Bean
    @ConditionalOnBean(DiscoveryClient.class)
    @ConditionalOnMissingBean
    public ServiceInstanceListSupplier discoveryClientServiceInstanceListSupplier(
        ConfigurableApplicationContext context, VLoadBalanceProperties vLoadBalanceProperties) {
        VReachableServiceInstanceListSupplier vReachableServiceInstanceListSupplier =
            new VReachableServiceInstanceListSupplier(ServiceInstanceListSupplier.builder()
                .withBlockingDiscoveryClient().with((applicationContext, delegate) -> {
                    LoadBalancerZoneConfig zoneConfig = applicationContext.getBean(LoadBalancerZoneConfig.class);
                    LoadBalanceConfigService loadBalanceConfigService =
                        applicationContext.getBean(LoadBalanceConfigService.class);
                    return new CustomServiceInstanceListSupplier(delegate, zoneConfig, loadBalanceConfigService);
                }).build(context), vLoadBalanceProperties, context);
        vReachableServiceInstanceListSupplier.setName(context.getDisplayName());
        return vReachableServiceInstanceListSupplier;
    }

}
