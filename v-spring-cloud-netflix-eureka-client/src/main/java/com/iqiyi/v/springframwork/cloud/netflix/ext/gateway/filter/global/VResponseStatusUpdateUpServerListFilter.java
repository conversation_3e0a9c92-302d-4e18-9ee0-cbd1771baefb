package com.iqiyi.v.springframwork.cloud.netflix.ext.gateway.filter.global;

import com.google.common.collect.Lists;
import com.iqiyi.v.springframwork.cloud.client.constants.VEurekaClientConstants;
import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.p.VLoadBalanceProperties;
import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.utils.VEvictionServers;
import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.utils.VLogs;
import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.utils.VServerWebExchangeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.loadbalancer.reactive.ReactiveLoadBalancer;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.cloud.gateway.route.Route;
import org.springframework.cloud.loadbalancer.support.LoadBalancerClientFactory;
import org.springframework.core.Ordered;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.util.List;
import java.util.Set;

/**
 * 响应状态码捕获更新路由注册表
 *
 * <AUTHOR>
 * @date 2022/5/5 14:42
 */
@Slf4j
public class VResponseStatusUpdateUpServerListFilter implements GlobalFilter, Ordered {

    private LoadBalancerClientFactory loadBalancerClientFactory;
    private VLoadBalanceProperties vLoadBalanceProperties;

    public VResponseStatusUpdateUpServerListFilter(LoadBalancerClientFactory loadBalancerClientFactory, VLoadBalanceProperties vLoadBalanceProperties) {
        this.loadBalancerClientFactory = loadBalancerClientFactory;
        this.vLoadBalanceProperties = vLoadBalanceProperties;
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        return chain.filter(exchange)
//            .doOnError((throwable) -> vResLog(exchange, throwable))
            .doOnSuccess((Void) -> {
                try {
                    vResLog(exchange, null);

                    final Integer rawStatusCode;
                    List<String> list = exchange.getResponse() != null && exchange.getResponse().getHeaders() != null
                            && exchange.getResponse().getHeaders().get(VEurekaClientConstants.RES_INSTANCE_STATUS_KEY) != null ?
                            exchange.getResponse().getHeaders().get(VEurekaClientConstants.RES_INSTANCE_STATUS_KEY) : Lists.newArrayList();
                    rawStatusCode = !CollectionUtils.isEmpty(list) && NumberUtils.isDigits(list.get(0)) ? Integer.valueOf(list.get(0)) : VServerWebExchangeUtils.getRawStatusCode(exchange);
                    if (VEvictionServers.isEviction(rawStatusCode)) {
                        final Route route = VServerWebExchangeUtils.gatewayRoute(exchange);
                        ReactiveLoadBalancer<ServiceInstance> loadBalancer = loadBalancerClientFactory.getInstance(route.getUri().getHost());
                        URI requestRouteUrl = VServerWebExchangeUtils.gatewayRequestUrl(exchange);
                        VEvictionServers.evictionServ(route.getUri().getHost(), requestRouteUrl.getHost(), requestRouteUrl.getPort(), requestRouteUrl,
                            rawStatusCode, loadBalancer, vLoadBalanceProperties);
                    }
                } catch (Exception e) {
                    log.error("v-response status update up server list, exception.", e);
                }
            });
    }

    private void vResLog(ServerWebExchange exchange, Throwable throwable) {
        final URI requestRouteUrl = VServerWebExchangeUtils.gatewayRequestUrl(exchange);
        final Set<URI> originalRequestUrls = VServerWebExchangeUtils.gatewayOriginalRequestUrl(exchange);
        final Integer rawStatusCode = VServerWebExchangeUtils.getRawStatusCode(exchange);
        final URI requestUrl = VServerWebExchangeUtils.requestUrl(exchange);

        if (VLogs.isTrace(log) && ((rawStatusCode != null && HttpStatus.OK.value() != rawStatusCode) || (throwable != null))) {
            log.info("\n route : {}, original-request-url: {}, \n request-url: {}, \n response-httpStatus: {}, {}, {}",
                requestRouteUrl, originalRequestUrls, requestUrl, rawStatusCode, throwable == null ? "-1" : throwable.getMessage());
            if (throwable != null) {
                log.error("request exception.", throwable);
            }
        }
    }

    @Override
    public int getOrder() {
        return 100;
    }
}
