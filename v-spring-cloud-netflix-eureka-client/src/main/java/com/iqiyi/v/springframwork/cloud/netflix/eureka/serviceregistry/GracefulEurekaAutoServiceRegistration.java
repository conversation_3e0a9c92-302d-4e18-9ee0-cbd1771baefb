package com.iqiyi.v.springframwork.cloud.netflix.eureka.serviceregistry;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.boot.web.context.WebServerInitializedEvent;
import org.springframework.cloud.netflix.eureka.serviceregistry.EurekaAutoServiceRegistration;
import org.springframework.cloud.netflix.eureka.serviceregistry.EurekaRegistration;
import org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry;
import org.springframework.context.ApplicationContext;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.WebRequestInterceptor;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.handler.MappedInterceptor;
import org.springframework.web.servlet.handler.WebRequestHandlerInterceptorAdapter;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import com.iqiyi.v.springframwork.cloud.client.gracefulshutdown.GracefulShutDownInfoManager;
import com.iqiyi.v.springframwork.cloud.client.gracefulshutdown.Interceptor.GracefulShutDownInterceptor;
import com.iqiyi.v.springframwork.cloud.client.gracefulstart.GracefulStartInfoManager;

public class GracefulEurekaAutoServiceRegistration extends EurekaAutoServiceRegistration {

	private static final Log log = LogFactory.getLog(EurekaAutoServiceRegistration.class);

	private ApplicationContext context;

	private AtomicBoolean initialized = new AtomicBoolean(false);
	private AtomicBoolean warmedUp = new AtomicBoolean(false);
	private AtomicInteger serverPort = new AtomicInteger(0);


	private List<WarmUp> warmUps;

	private int warmUpTimeout;


	public GracefulEurekaAutoServiceRegistration(ApplicationContext context, EurekaServiceRegistry serviceRegistry, EurekaRegistration registration,List<WarmUp> warmUps,int warmUpTimeout,int port) {
		super(context, serviceRegistry, registration);
		this.context = context;
		this.warmUps = warmUps;
		this.warmUpTimeout = warmUpTimeout;
		this.serverPort.set(port);
	}

	@Override
	public void start() {
		if(!initialized.compareAndSet(false,true)){
			log.info("GracefulInterceptor has initialized");
			return;
		}
		//注册优雅下线拦截器
		registryGracefulInterceptor();
	}

	private void registryGracefulInterceptor() {
		try {
			log.info("start  init GracefulInterceptor");
			RequestMappingHandlerMapping requestMappingHandlerMapping = context.getBean("requestMappingHandlerMapping",
					RequestMappingHandlerMapping.class);
			boolean exGracefulInterceptor = false;
			Field field = RequestMappingHandlerMapping.class.getSuperclass()
					.getSuperclass().getSuperclass().getDeclaredField("interceptors");
			Field adaptedInterceptorsField = RequestMappingHandlerMapping.class
					.getSuperclass().getSuperclass().getSuperclass()
					.getDeclaredField("adaptedInterceptors");
			field.setAccessible(true);
			adaptedInterceptorsField.setAccessible(true);
			List<Object> interceptors = (List) field.get(requestMappingHandlerMapping);
			Iterator iterator = interceptors.iterator();
			List<Object> _interceptors = new ArrayList<>();
			List<HandlerInterceptor> adaptedInterceptors = new ArrayList<HandlerInterceptor>();
			if (!CollectionUtils.isEmpty(interceptors)) {
				if (interceptors.get(0) instanceof MappedInterceptor
						&& ((MappedInterceptor) interceptors.get(0))
						.getInterceptor() instanceof GracefulShutDownInterceptor) {
					return;
				}
				while (iterator.hasNext()) {
					Object interceptor = iterator.next();
					if (interceptor instanceof MappedInterceptor
							&& ((MappedInterceptor) interceptor)
							.getInterceptor() instanceof GracefulShutDownInterceptor) {
						_interceptors.add(interceptor);
						iterator.remove();
						exGracefulInterceptor = true;
						break;
					}
				}
				if (!exGracefulInterceptor) {
					log.warn(
							"suggest using this way to config mvc interceptors,just to see:https://docs.spring.io/spring-framework/docs/current/reference/html/web.html#mvc-config-interceptors");
					GracefulShutDownInterceptor gracefulShutDownInterceptor = new GracefulShutDownInterceptor();
					String[] includePatterns = GracefulShutDownInfoManager.getInstance()
							.getIncludePathPatterns();
					String[] excludePatterns = GracefulShutDownInfoManager.getInstance()
							.getExcludePathPatterns();
					MappedInterceptor mappedInterceptor = new MappedInterceptor(
							includePatterns, excludePatterns, gracefulShutDownInterceptor);
					_interceptors.add(mappedInterceptor);
				}
				_interceptors.addAll(interceptors);
				if (!_interceptors.isEmpty()) {
					for (int i = 0; i < _interceptors.size(); i++) {
						Object interceptor = _interceptors.get(i);
						if (interceptor == null) {
							throw new IllegalArgumentException("Entry number " + i
									+ " in interceptors array is null");
						}
						adaptedInterceptors.add(adaptInterceptor(interceptor));
					}
				}
				field.set(requestMappingHandlerMapping, _interceptors);
				adaptedInterceptorsField.set(requestMappingHandlerMapping,
						adaptedInterceptors);
			}

		}
		catch (IllegalAccessException e) {

		}
		catch (NoSuchFieldException e) {
			log.error("NoSuchFieldException", e);
		}
		catch (Exception e) {
			log.error("Exception", e);
		}
	}

	private void asyncWarmup() {
		ExecutorService executor = Executors.newSingleThreadExecutor(
				r -> {
					Thread thread = new Thread(r, "EurekaClient-ServiceWarmUp");
					thread.setDaemon(true);
					return thread;
				}
		);
		try {
			WarmupTask warmupTask = new WarmupTask(warmUps);
		    Future future = executor.submit(warmupTask);
			Object object = future.get(warmUpTimeout, TimeUnit.SECONDS);
			GracefulStartInfoManager.getInstance().setWarmedUp(true);
			log.info("delay registry service and warn up service,res:"+object.toString());
		} catch (Exception e) {
			log.info("delay registry service and warn up service error",e);
		}finally {
			if (!executor.isShutdown()){
				executor.shutdown();
			}
		}

	}


	@Override
	public void stop() {
		if (GracefulShutDownInfoManager.getInstance().isGracefulShutDownEnabled()) {
			GracefulShutDownInfoManager.getInstance().setStopping();
		}
		long start = System.currentTimeMillis();
		super.stop();
		int waitTimeoutMillis = GracefulShutDownInfoManager.getInstance()
				.getWaitTimeoutMillis();
		log.info("server stop waitTimeoutMillis" + waitTimeoutMillis);
		// simple way
		if (GracefulShutDownInfoManager.getInstance().isGracefulShutDownEnabled()) {
			try {
				Thread.sleep(waitTimeoutMillis);
			}
			catch (InterruptedException e) {
				Thread.currentThread().interrupt();
			}
		}
		long end = System.currentTimeMillis();
		log.info("stop wait millis:" + (end - start));
	}

	public void onApplicationEvent(WebServerInitializedEvent event) {
		//tomcat 启动完成，执行预热逻辑
		boolean isGracefulStartEnabled = GracefulStartInfoManager.getInstance().isGracefulStartEnabled();
		int _tomcatServerPort = GracefulStartInfoManager.getInstance().getTomcatServerPort();
		int _port = event.getWebServer().getPort();
		if(isGracefulStartEnabled && _tomcatServerPort == _port && warmedUp.compareAndSet(false,true)){
			asyncWarmup();
		}

		FlowWarmUpParamsProcessor.addFlowWarmUpParamsToConfigMetadataMap(context);

		//服务注册
		super.start();
	}

	private HandlerInterceptor adaptInterceptor(Object interceptor) {
		if (interceptor instanceof HandlerInterceptor) {
			return (HandlerInterceptor) interceptor;
		}
		else if (interceptor instanceof WebRequestInterceptor) {
			return new WebRequestHandlerInterceptorAdapter(
					(WebRequestInterceptor) interceptor);
		}
		else {
			throw new IllegalArgumentException("Interceptor type not supported: "
					+ interceptor.getClass().getName());
		}
	}

	public void setServerPort(int port){
		this.serverPort.compareAndSet(0,port);
	}
}
