package com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.loadbalance;

import java.util.List;
import java.util.Optional;

import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.loadbalance.LoadBalanceConfig.Config;

/**
 * <AUTHOR>
 * @date 2023/11/3 18:13
 */
public interface LoadBalanceConfigService {

    /**
     * 根据服务名查询路由策略配置
     *
     * @param serviceName 服务名，不区分大小写
     * @return 路由策略配置
     */
    Optional<LoadBalanceConfig> query(String serviceName);

    /**
     * 根据服务名查询路由策略配置
     *
     * @param serviceName 服务名，不区分大小写
     * @param zone 应用所在zone
     * @return 路由策略配置
     */
    List<Config> query(String serviceName, String zone);

    /**
     * 注册zone权重变化监听器
     *
     * @param serviceName 服务名，不区分大小写
     * @param loadBalanceConfigChangeListener 监听器
     */
    void addLoadBalanceConfigChangeListener(String serviceName, LoadBalanceConfigChangeListener loadBalanceConfigChangeListener);


}
