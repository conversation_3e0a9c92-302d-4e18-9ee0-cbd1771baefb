package com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.loadbalance.core;

import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.loadbalance.RecentlyEvictionServer;
import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.loadbalance.VReachableServiceInstanceListSupplier;
import com.netflix.appinfo.InstanceInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.loadbalancer.core.DelegatingServiceInstanceListSupplier;
import org.springframework.cloud.loadbalancer.core.NoopServiceInstanceListSupplier;
import org.springframework.cloud.loadbalancer.core.RoundRobinLoadBalancer;
import org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier;
import org.springframework.cloud.netflix.eureka.EurekaServiceInstance;
import reactor.core.publisher.Flux;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Desc
 * @date 2023/1/31 15:04
 */
@Slf4j
public class VRoundRobinLoadBalancer extends RoundRobinLoadBalancer {

    ObjectProvider<ServiceInstanceListSupplier> serviceInstanceListSupplierProvider;

    public VRoundRobinLoadBalancer(ObjectProvider<ServiceInstanceListSupplier> serviceInstanceListSupplierProvider, String serviceId) {
        super(serviceInstanceListSupplierProvider, serviceId);
        this.serviceInstanceListSupplierProvider = serviceInstanceListSupplierProvider;
    }

    public VReachableServiceInstanceListSupplier getServiceInstanceListSupplier() {
        ServiceInstanceListSupplier supplier = serviceInstanceListSupplierProvider.getIfAvailable(NoopServiceInstanceListSupplier::new);
        while (supplier != null){
            if(!(supplier instanceof DelegatingServiceInstanceListSupplier)){
                throw new UnsupportedOperationException("ServiceInstanceListSupplier not instanceof DelegatingServiceInstanceListSupplier");
            }
            if (supplier instanceof VReachableServiceInstanceListSupplier) {
                return (VReachableServiceInstanceListSupplier) supplier;
            }
            supplier = ((DelegatingServiceInstanceListSupplier) supplier).getDelegate();
        }
        throw new UnsupportedOperationException("ServiceInstanceListSupplier not instanceof DelegatingServiceInstanceListSupplier");
    }

    public Flux<List<ServiceInstance>> getAllServiceInstancesFlux() {
        return this.getServiceInstanceListSupplier().getAllServiceInstances();
    }

    public List<ServiceInstance> getReachableServiceInstances() {
        return Collections.unmodifiableList(this.getServiceInstanceListSupplier().getReachableServiceInstances());
    }

    public void addRecentlyEvictionServers(String host, int port, String appName) {
        final RecentlyEvictionServer recentlyEvictionServer = new RecentlyEvictionServer(System.currentTimeMillis(), new EurekaServiceInstance(
                InstanceInfo.Builder.newBuilder().setHostName(host).setPort(port).setAppName(appName).build()
        ));
        this.getServiceInstanceListSupplier().addRecentlyEvictionServers(recentlyEvictionServer);
    }
}
