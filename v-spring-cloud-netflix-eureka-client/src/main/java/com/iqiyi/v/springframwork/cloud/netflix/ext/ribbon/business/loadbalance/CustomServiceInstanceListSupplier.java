package com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.loadbalance;

import com.iqiyi.v.springframwork.cloud.client.constants.VEurekaClientConstants;
import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.loadbalance.LoadBalanceConfig.Config;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.loadbalancer.config.LoadBalancerZoneConfig;
import org.springframework.cloud.loadbalancer.core.DelegatingServiceInstanceListSupplier;
import org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 根据自定义路由配置过滤实例列表
 * <AUTHOR>
 * @date 2023/11/12 19:55
 */
public class CustomServiceInstanceListSupplier extends DelegatingServiceInstanceListSupplier {

    private static final String ZONE = "zone";

    private static final String DEFAULT_ZONE = "UNKNOWN";

    private final LoadBalancerZoneConfig zoneConfig;

    private String appZone;

    private final LoadBalanceConfigService loadBalanceConfigService;

    public CustomServiceInstanceListSupplier(ServiceInstanceListSupplier delegate,
        LoadBalancerZoneConfig zoneConfig, LoadBalanceConfigService loadBalanceConfigService) {
        super(delegate);
        this.zoneConfig = zoneConfig;
        this.loadBalanceConfigService = loadBalanceConfigService;
    }

    @Override
    public Flux<List<ServiceInstance>> get() {
        return getDelegate().get().map(this::filteredByZoneConfig);
    }

    private List<ServiceInstance> filteredByZoneConfig(List<ServiceInstance> serviceInstances) {
        if (appZone == null) {
            appZone = zoneConfig.getZone();
        }
        
        if (VEurekaClientConstants.ZONE_PRE.equals(appZone)) {
            // 如果当前应用部署在预发环境，则不对服务实例进行过滤（压测流量调用生产实例，非压测流量要调用预发实例）
            serviceInstances
                .forEach(serviceInstance -> serviceInstance.getMetadata().put(VEurekaClientConstants.WEIGHT_KEY, "1"));
            return serviceInstances;
        }
        
        if (appZone != null && !serviceInstances.isEmpty()) {
            List<ServiceInstance> filteredInstances = new ArrayList<>();
            // 查询配置的自定义路由信息
            List<Config> configList = loadBalanceConfigService.query(getServiceId(), appZone);
            // 先根据正常路由配置筛选服务实例
            List<Config> normalConfigs = configList.stream().filter(config -> !config.isBackup()).collect(Collectors.toList());
            Map<String, List<ServiceInstance>> zoneServerMap = serviceInstances.stream()
                .collect(Collectors.groupingBy(this::getZone));
            addMatchedZoneServers(normalConfigs, zoneServerMap, filteredInstances);
            if (!filteredInstances.isEmpty()) {
                return filteredInstances;
            }
            // 根据备份路由配置筛选服务实例，若筛选后为空，则返回全部实例
            List<Config> backupConfigs = configList.stream().filter(Config::isBackup).collect(Collectors.toList());
            addMatchedZoneServers(backupConfigs, zoneServerMap, filteredInstances);
            if (!filteredInstances.isEmpty()) {
                return filteredInstances;
            }
        }
        // If the zone is not set or there are no zone-specific instances available,
        // we return all instances retrieved for given service id.
        return serviceInstances;
    }

    private void addMatchedZoneServers(List<Config> configs, Map<String, List<ServiceInstance>> zoneServerMap,
        List<ServiceInstance> filteredServers) {
        configs.forEach(config -> {
            List<ServiceInstance> instanceList = zoneServerMap.get(config.getZone());
            if (instanceList == null || instanceList.isEmpty()) {
                return;
            }
            if (instanceList.size() >= config.getMinInstances()) {
                instanceList.stream().sorted(Comparator.comparing(ServiceInstance::getInstanceId))
                    .forEach(serviceInstance -> {
                        // 为每个server设置权重信息，后面处理过程中可以直接使用，避免再次查询导致潜在的不一致性
                        serviceInstance.getMetadata().put(VEurekaClientConstants.WEIGHT_KEY,
                            String.valueOf(config.getWeight()));
                        filteredServers.add(serviceInstance);
                    });
            }
        });
    }

    private String getZone(ServiceInstance serviceInstance) {
        String zone = null;
        Map<String, String> metadata = serviceInstance.getMetadata();
        if (metadata != null) {
            zone = metadata.get(ZONE);
        }
        return zone != null ? zone : DEFAULT_ZONE;
    }
}
