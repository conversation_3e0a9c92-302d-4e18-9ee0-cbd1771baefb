package com.iqiyi.v.springframwork.cloud.netflix.eureka.serviceregistry.webflux.warmup;

import org.springframework.cloud.client.serviceregistry.Registration;

import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

import com.iqiyi.v.springframwork.cloud.netflix.eureka.serviceregistry.webflux.serviceregistry.RegistrationLifecycle;


/**
 * <AUTHOR>
 */
public class WarmUpRegistrationLifeCycle implements RegistrationLifecycle {

    protected final AtomicBoolean running = new AtomicBoolean();
    private final List<WarmUpAction> warmUpActions;

    public WarmUpRegistrationLifeCycle(List<WarmUpAction> warmUpActions) {
        this.warmUpActions = warmUpActions;
    }

    @Override
    public void preRegister(Registration registration) {
        if (running.compareAndSet(false, true)) {
            warmUpActions.forEach(WarmUpAction::warmup);
        }
    }

}
