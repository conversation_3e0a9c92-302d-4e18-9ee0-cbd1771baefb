package com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.loadbalance;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 参考Nginx实现的平滑加权轮询算法
 *
 * <AUTHOR>
 * @date 2023/10/31 13:44
 */
public class SmoothWeightedRoundRobin<T> {

    private final List<WeightedServer<T>> weightedServerList;
    private final ReentrantLock lock = new ReentrantLock();

    public SmoothWeightedRoundRobin(List<WeightedServer<T>> weightedServers) {
        this.weightedServerList = weightedServers;
    }

    public Optional<T> select() {
        try {
            lock.lock();
            return this.selectInner();
        } finally {
            lock.unlock();
        }
    }

    private Optional<T> selectInner() {
        if (weightedServerList.isEmpty()) {
            return Optional.empty();
        }

        int totalWeight = 0;
        int maxWeight = 0;
        WeightedServer<T> maxWeightedServer = null;
        for (WeightedServer<T> weightedServer : weightedServerList) {
            int weight = weightedServer.getWeight();
            totalWeight += weight;

            // 每个节点的当前权重要加上原始的权重
            weightedServer.setCurrentWeight(weightedServer.getCurrentWeight() + weight);

            // 保存当前权重最大的节点
            if (maxWeightedServer == null || maxWeight < weightedServer.getCurrentWeight()) {
                maxWeightedServer = weightedServer;
                maxWeight = weightedServer.getCurrentWeight();
            }
        }
        // 被选中的节点权重减掉总权重
        maxWeightedServer.setCurrentWeight(maxWeightedServer.getCurrentWeight() - totalWeight);
        return Optional.of(maxWeightedServer.getServer());
    }

    public List<WeightedServer<T>> getWeightedServerList() {
        return weightedServerList;
    }

    public static class WeightedServer<T> {

        /**
         * 权重系数
         */
        private static final int DEFAULT_WEIGHT_FACTOR = 100;

        /**
         * 初始权重（保持不变）
         */
        private final int weight;

        /**
         * 服务实例
         */
        private final T server;

        /**
         * 当前权重
         */
        private int currentWeight;

        /**
         * 启动时间
         */
        private final long startTimestamp;

        /**
         * 预热时间
         */
        private final int flowWarmUpTimeSeconds;

        /**
         * 是否需要预热
         */
        private boolean warmUpFinished;

        public WeightedServer(T server, int weight, long startTimestamp, int flowWarmUpTimeSeconds) {
            this.server = server;
            this.weight = weight * DEFAULT_WEIGHT_FACTOR;
            this.startTimestamp = startTimestamp;
            this.flowWarmUpTimeSeconds = flowWarmUpTimeSeconds;
            this.warmUpFinished = startTimestamp <= 0 || flowWarmUpTimeSeconds <= 0;
        }

        public int getCurrentWeight() {
            return currentWeight;
        }

        public int getWeight() {
            if (warmUpFinished) {
                return weight;
            }

            long duration = Instant.now().toEpochMilli() - startTimestamp;
            if (duration <= 0) {
                return 0;
            }

            long flowWarmUpTimeMillis = flowWarmUpTimeSeconds * 1000L;
            if (duration >= flowWarmUpTimeMillis) {
                this.warmUpFinished = true;
                return weight;
            }

            long scaledWeight = duration * weight;
            return (int) Math.ceil((double) scaledWeight / flowWarmUpTimeMillis);
        }

        public void setCurrentWeight(int currentWeight) {
            this.currentWeight = currentWeight;
        }

        public T getServer() {
            return server;
        }

        @Override
        public String toString() {
            return "WeightedServer{" +
                    "weight=" + weight +
                    ", server=" + server +
                    ", currentWeight=" + currentWeight +
                    ", startTimestamp=" + startTimestamp +
                    ", flowWarmUpTimeMillis=" + flowWarmUpTimeSeconds +
                    ", warmUpFinished=" + warmUpFinished +
                    '}';
        }
    }
}

