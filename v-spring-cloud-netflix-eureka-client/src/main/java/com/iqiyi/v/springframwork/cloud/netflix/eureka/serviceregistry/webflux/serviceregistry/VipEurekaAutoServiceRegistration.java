package com.iqiyi.v.springframwork.cloud.netflix.eureka.serviceregistry.webflux.serviceregistry;

import com.iqiyi.v.springframwork.cloud.netflix.eureka.serviceregistry.FlowWarmUpParamsProcessor;
import org.springframework.cloud.netflix.eureka.serviceregistry.EurekaAutoServiceRegistration;
import org.springframework.cloud.netflix.eureka.serviceregistry.EurekaRegistration;
import org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry;
import org.springframework.context.ApplicationContext;
import org.springframework.core.annotation.AnnotationAwareOrderComparator;

import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/24 10:55
 */
public class VipEurekaAutoServiceRegistration extends EurekaAutoServiceRegistration {

    private final AtomicBoolean running = new AtomicBoolean(false);
    private final ApplicationContext context;
    private final EurekaRegistration registration;

    public VipEurekaAutoServiceRegistration(ApplicationContext context, EurekaServiceRegistry serviceRegistry, EurekaRegistration registration) {
        super(context, serviceRegistry, registration);
        this.context = context;
        this.registration = registration;
    }

    @Override
    public void start() {
        if (this.running.compareAndSet(false, true)) {
            List<RegistrationLifecycle> lifecycles = context.getBeanProvider(RegistrationLifecycle.class).stream().collect(Collectors.toList());
            AnnotationAwareOrderComparator.sort(lifecycles);
            lifecycles.forEach(lifecycle -> lifecycle.preRegister(registration));
            FlowWarmUpParamsProcessor.addFlowWarmUpParamsToConfigMetadataMap(context);
            super.start();
            lifecycles.forEach(lifecycle -> lifecycle.postRegister(registration));
        } else {
            super.start();
        }
    }

    @Override
    public void stop(Runnable callback) {
        if (this.running.compareAndSet(true, false)) {
            List<DeregistrationLifecycle> lifecycles = context.getBeanProvider(DeregistrationLifecycle.class).stream().collect(Collectors.toList());
            AnnotationAwareOrderComparator.sort(lifecycles);
            lifecycles.forEach(lifecycle -> lifecycle.preDeregister(registration));
            super.stop();
            lifecycles.forEach(lifecycle -> lifecycle.postDeregister(registration));
        } else {
            super.stop();
        }
    }
}
