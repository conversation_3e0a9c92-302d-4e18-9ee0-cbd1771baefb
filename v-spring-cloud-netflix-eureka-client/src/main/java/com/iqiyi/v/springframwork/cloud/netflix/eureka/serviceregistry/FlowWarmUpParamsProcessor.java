package com.iqiyi.v.springframwork.cloud.netflix.eureka.serviceregistry;

import com.iqiyi.v.springframwork.cloud.client.constants.VEurekaClientConstants;
import com.netflix.appinfo.EurekaInstanceConfig;
import org.springframework.context.ApplicationContext;

import java.time.Instant;

/**
 * <AUTHOR>
 */
public class FlowWarmUpParamsProcessor {

    public static void addFlowWarmUpParamsToConfigMetadataMap(ApplicationContext context) {
        EurekaInstanceConfig eurekaInstanceConfig = context.getBean(EurekaInstanceConfig.class);
        // 服务启动时间
        eurekaInstanceConfig.getMetadataMap().put(VEurekaClientConstants.START_TIMESTAMP_KEY,
            String.valueOf(Instant.now().toEpochMilli()));

        // 流量预热时间
        String flowWarmUpTimeMillis =
            eurekaInstanceConfig.getMetadataMap().get(VEurekaClientConstants.FLOW_WARM_UP_TIME_SECONDS_KEY);
        if (flowWarmUpTimeMillis == null || flowWarmUpTimeMillis.trim().isEmpty()) {
            eurekaInstanceConfig.getMetadataMap().put(VEurekaClientConstants.FLOW_WARM_UP_TIME_SECONDS_KEY,
                String.valueOf(VEurekaClientConstants.DEFAULT_FLOW_WARM_UP_TIME_SECONDS));
        } else if (Integer.parseInt(flowWarmUpTimeMillis) < VEurekaClientConstants.MIN_FLOW_WARM_UP_TIME_SECONDS) {
            eurekaInstanceConfig.getMetadataMap().put(VEurekaClientConstants.FLOW_WARM_UP_TIME_SECONDS_KEY,
                String.valueOf(VEurekaClientConstants.MIN_FLOW_WARM_UP_TIME_SECONDS));
        }
    }

}
