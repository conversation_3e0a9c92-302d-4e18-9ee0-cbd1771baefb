package com.iqiyi.v.springframwork.cloud.netflix.ext.autoconfig;

import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.VRibbonApplicationContextInitializer;
import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.VRibbonExceptionServerInterceptor;
import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.VRibbonRequestTransformer;
import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.loadbalance.LoadBalanceConfigService;
import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.loadbalance.LoadBalanceConfigServiceImpl;
import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.p.VLoadBalanceProperties;
import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.p.VRibbonProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.loadbalancer.AsyncLoadBalancerAutoConfiguration;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.cloud.client.loadbalancer.LoadBalancerClientsProperties;
import org.springframework.cloud.client.loadbalancer.LoadBalancerRequestTransformer;
import org.springframework.cloud.loadbalancer.annotation.LoadBalancerClients;
import org.springframework.cloud.loadbalancer.support.LoadBalancerClientFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Slf4j
@LoadBalancerClients(defaultConfiguration = LoadBalanceConfiguration.class)
@EnableConfigurationProperties({VRibbonProperties.class, VLoadBalanceProperties.class, LoadBalancerClientsProperties.class})
@ConditionalOnProperty(prefix = "v-ribbon", value = "enabled", matchIfMissing = true)
@ConditionalOnMissingClass("org.springframework.cloud.gateway.filter.factory.GatewayFilterFactory")
@AutoConfigureAfter(value = {org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration.class,
        AsyncLoadBalancerAutoConfiguration.class})
public class BusinessLoadBalanceAutoConfiguration {

    @LoadBalanced
    @Autowired(required = false)
    private List<RestTemplate> restTemplates = Collections.emptyList();

    @Autowired
    private VRibbonProperties vRibbonProperties;

    @Bean
    public SmartInitializingSingleton vRibbonRestTemplateCustomizer(
            LoadBalancerClientFactory loadBalancerClientFactory, VLoadBalanceProperties vLoadBalanceProperties) {
        return () -> {
            for (RestTemplate restTemplate : BusinessLoadBalanceAutoConfiguration.this.restTemplates) {
                List<ClientHttpRequestInterceptor> interceptorList = new ArrayList<>(restTemplate.getInterceptors());
                interceptorList.add(new VRibbonExceptionServerInterceptor(loadBalancerClientFactory, vLoadBalanceProperties));
                restTemplate.setInterceptors(interceptorList);
            }
            log.info(restTemplates != null && restTemplates.size() > 0 ? "RestTemplate已支持可靠负载。" : "RestTemplate未支持可靠负载。");
        };
    }

    @Bean
    public LoadBalancerRequestTransformer vRibbonRequestTransformer() {
        return new VRibbonRequestTransformer();
    }

    @Bean
    public VRibbonApplicationContextInitializer vRibbonApplicationContextInitializer(LoadBalancerClientFactory loadBalancerClientFactory) {

        return new VRibbonApplicationContextInitializer(loadBalancerClientFactory, vRibbonProperties.getServiceIdList());
    }

    @Bean
    @ConditionalOnMissingBean
    public LoadBalanceConfigService loadBalanceConfigService() {
        return new LoadBalanceConfigServiceImpl();
    }

}  