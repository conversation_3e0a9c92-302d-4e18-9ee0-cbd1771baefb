package com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.loadbalance.support;

import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.loadbalancer.LoadBalancerClientsProperties;
import org.springframework.cloud.client.loadbalancer.reactive.ReactiveLoadBalancer;
import org.springframework.cloud.loadbalancer.support.LoadBalancerClientFactory;

/**
 * <AUTHOR>
 * @version 1.0
 * @Desc
 * @date 2023/2/8 19:45
 */
public class VLoadBalancerClientFactory extends LoadBalancerClientFactory {

    public VLoadBalancerClientFactory(LoadBalancerClientsProperties properties) {
        super(properties);
    }

    @Override
    public ReactiveLoadBalancer<ServiceInstance> getInstance(String serviceId) {
        return super.getInstance(serviceId.toLowerCase());
    }
}
