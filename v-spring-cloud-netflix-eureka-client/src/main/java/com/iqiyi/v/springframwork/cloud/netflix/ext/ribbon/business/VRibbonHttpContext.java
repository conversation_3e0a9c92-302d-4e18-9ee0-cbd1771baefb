package com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business;

import org.apache.commons.lang.BooleanUtils;
import org.springframework.cloud.client.DefaultServiceInstance;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.http.HttpRequest;
import org.springframework.util.NumberUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.ClientRequest;

/**
 *
 */
public class VRibbonHttpContext {

    private final static String X_HTTP_HEADER_KEY_EUREKA_INSTANCE_ID = "X-Eureka-Instance-Id";
    private final static String X_HTTP_HEADER_KEY_EUREKA_SERVICE_ID = "X-Eureka-Service-Id";
    private static final String X_HTTP_HEADER_KEY_EUREKA_HOST = "X-Eureka-Host";
    private static final String X_HTTP_HEADER_KEY_EUREKA_Port = "X-Eureka-Port";
    private static final String X_HTTP_HEADER_KEY_EUREKA_SECURE = "X-Eureka-Secure";

    /**
     *
     */
    public static ServiceInstance getServiceInstance(HttpRequest httpRequest) {
        String serviceId = httpRequest.getHeaders().getFirst(X_HTTP_HEADER_KEY_EUREKA_SERVICE_ID);
        if (serviceId == null || serviceId.isEmpty()) {
            return null;
        }

        String host = httpRequest.getHeaders().getFirst(X_HTTP_HEADER_KEY_EUREKA_HOST);
        if (host == null || host.isEmpty()) {
            return null;
        }

        String portStr = httpRequest.getHeaders().getFirst(X_HTTP_HEADER_KEY_EUREKA_Port);
        if (portStr == null || portStr.isEmpty()) {
            return null;
        }
        int port = NumberUtils.parseNumber(portStr, Integer.class);

        boolean secure = BooleanUtils.toBoolean(httpRequest.getHeaders().getFirst(X_HTTP_HEADER_KEY_EUREKA_SECURE));
        return new DefaultServiceInstance(httpRequest.getHeaders().getFirst(X_HTTP_HEADER_KEY_EUREKA_INSTANCE_ID),
            serviceId, host, port, secure);
    }

    /**
     *
     */
    public static ServiceInstance getServiceInstance(ClientRequest httpRequest) {
        String serviceId = httpRequest.headers().getFirst(X_HTTP_HEADER_KEY_EUREKA_SERVICE_ID);
        if (serviceId == null) {
            return null;
        }
        String host = httpRequest.headers().getFirst(X_HTTP_HEADER_KEY_EUREKA_HOST);
        int port = NumberUtils.parseNumber(httpRequest.headers().getFirst(X_HTTP_HEADER_KEY_EUREKA_Port), Integer.class);
        boolean secure = BooleanUtils.toBoolean(httpRequest.headers().getFirst(X_HTTP_HEADER_KEY_EUREKA_SECURE));
        if (StringUtils.isEmpty(serviceId)) {
            return null;
        }
        ServiceInstance serviceInstance = new DefaultServiceInstance(httpRequest.headers().getFirst(X_HTTP_HEADER_KEY_EUREKA_INSTANCE_ID),
            serviceId, host, port, secure);

        return serviceInstance;
    }

    /**
     * 保存ServiceInstance到Request
     */
    public static HttpRequest saveServiceInstance(HttpRequest httpRequest, ServiceInstance serviceInstance) {
        httpRequest.getHeaders().set(X_HTTP_HEADER_KEY_EUREKA_INSTANCE_ID, serviceInstance.getInstanceId());
        httpRequest.getHeaders().set(X_HTTP_HEADER_KEY_EUREKA_SERVICE_ID, serviceInstance.getServiceId());
        httpRequest.getHeaders().set(X_HTTP_HEADER_KEY_EUREKA_HOST, serviceInstance.getHost());
        httpRequest.getHeaders().set(X_HTTP_HEADER_KEY_EUREKA_Port, String.valueOf(serviceInstance.getPort()));
        httpRequest.getHeaders().set(X_HTTP_HEADER_KEY_EUREKA_SECURE, String.valueOf(serviceInstance.isSecure()));
        return httpRequest;
    }

    /**
     * 保存ServiceInstance到Request
     */
    public static ClientRequest saveServiceInstance(ClientRequest httpRequest, ServiceInstance serviceInstance) {
        return ClientRequest.from(httpRequest)
            .header(X_HTTP_HEADER_KEY_EUREKA_INSTANCE_ID, serviceInstance.getInstanceId())
            .header(X_HTTP_HEADER_KEY_EUREKA_SERVICE_ID, serviceInstance.getServiceId())
            .header(X_HTTP_HEADER_KEY_EUREKA_HOST, serviceInstance.getHost())
            .header(X_HTTP_HEADER_KEY_EUREKA_Port, String.valueOf(serviceInstance.getPort()))
            .header(X_HTTP_HEADER_KEY_EUREKA_SECURE, String.valueOf(serviceInstance.isSecure()))
            .build();

    }

}
