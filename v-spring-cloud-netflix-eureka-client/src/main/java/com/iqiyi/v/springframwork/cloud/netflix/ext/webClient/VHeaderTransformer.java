/*
 * Copyright 2012-2020 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.iqiyi.v.springframwork.cloud.netflix.ext.webClient;

import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerClientRequestTransformer;
import org.springframework.web.reactive.function.client.ClientRequest;

import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.VRibbonHttpContext;

/**
 * To add X-Forwarded-Host and X-Forwarded-Proto Headers.
 *
 * <AUTHOR>
 * <AUTHOR>
 * @since 3.1.0
 */

public class VHeaderTransformer implements LoadBalancerClientRequestTransformer {

    public VHeaderTransformer() {
    }

    @Override
    public ClientRequest transformRequest(ClientRequest request, ServiceInstance instance) {
        if (instance != null) {
            return VRibbonHttpContext.saveServiceInstance(request, instance);
        }
        return request;
    }

}
