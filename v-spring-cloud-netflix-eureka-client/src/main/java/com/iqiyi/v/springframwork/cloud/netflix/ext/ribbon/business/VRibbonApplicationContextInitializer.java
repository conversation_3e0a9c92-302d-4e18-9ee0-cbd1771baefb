package com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business;

import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.loadbalance.core.VRoundRobinLoadBalancer;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.loadbalancer.reactive.ReactiveLoadBalancer;
import org.springframework.cloud.loadbalancer.support.LoadBalancerClientFactory;
import org.springframework.context.ApplicationListener;

import java.util.List;

/**
 * Responsible for eagerly creating the child application context holding the Ribbon
 * related configuration
 *
 * <AUTHOR>
 */
public class VRibbonApplicationContextInitializer
		implements ApplicationListener<ApplicationReadyEvent> {

	private final LoadBalancerClientFactory loadBalancerClientFactory;

	
	//List of Ribbon client names
	private final List<String> clientNames;

	public VRibbonApplicationContextInitializer(LoadBalancerClientFactory loadBalancerClientFactory,
			List<String> clientNames) {
		this.loadBalancerClientFactory = loadBalancerClientFactory;
		this.clientNames = clientNames;
	}

	private void initialize() {
		if (clientNames != null) {
			for (String clientName : clientNames) {
                //懒加载容器,不能删除
				ReactiveLoadBalancer<ServiceInstance> loadBalancer = loadBalancerClientFactory.getInstance(clientName);
				VRoundRobinLoadBalancer vLoadBalancer = (VRoundRobinLoadBalancer) loadBalancer;
			}
		}
	}

	public void onApplicationEvent(ApplicationReadyEvent event) {
		initialize();
	}
}
