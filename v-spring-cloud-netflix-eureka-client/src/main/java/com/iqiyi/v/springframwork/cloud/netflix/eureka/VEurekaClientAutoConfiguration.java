package com.iqiyi.v.springframwork.cloud.netflix.eureka;

import com.netflix.appinfo.ApplicationInfoManager;
import com.netflix.appinfo.HealthCheckHandler;
import com.netflix.discovery.EurekaClient;
import com.netflix.discovery.EurekaClientConfig;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.serviceregistry.AutoServiceRegistration;
import org.springframework.cloud.netflix.eureka.CloudEurekaInstanceConfig;
import org.springframework.cloud.netflix.eureka.serviceregistry.EurekaAutoServiceRegistration;
import org.springframework.cloud.netflix.eureka.serviceregistry.EurekaRegistration;
import org.springframework.cloud.netflix.eureka.serviceregistry.EurekaServiceRegistry;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import java.util.List;

import com.iqiyi.v.springframwork.cloud.client.gracefulshutdown.GracefulShutDownInfoManager;
import com.iqiyi.v.springframwork.cloud.client.gracefulshutdown.Interceptor.GracefulShutDownInterceptor;
import com.iqiyi.v.springframwork.cloud.client.gracefulshutdown.Interceptor.InterceptorConfig;
import com.iqiyi.v.springframwork.cloud.client.gracefulstart.GracefulStartInfoManager;
import com.iqiyi.v.springframwork.cloud.client.serviceregistry.ServiceGracefulShutDownProperties;
import com.iqiyi.v.springframwork.cloud.client.serviceregistry.VAutoServiceRegistrationProperties;
import com.iqiyi.v.springframwork.cloud.netflix.eureka.serviceregistry.GracefulEurekaAutoServiceRegistration;
import com.iqiyi.v.springframwork.cloud.netflix.eureka.serviceregistry.WarmUp;
import com.iqiyi.v.springframwork.cloud.netflix.eureka.serviceregistry.webflux.serviceregistry.VipEurekaAutoServiceRegistration;
import com.iqiyi.v.springframwork.cloud.netflix.eureka.serviceregistry.webflux.shutdown.GracefulShutDownWebFilter;
import com.iqiyi.v.springframwork.cloud.netflix.eureka.serviceregistry.webflux.shutdown.ShutDownEventListener;
import com.iqiyi.v.springframwork.cloud.netflix.eureka.serviceregistry.webflux.warmup.WarmUpAction;
import com.iqiyi.v.springframwork.cloud.netflix.eureka.serviceregistry.webflux.warmup.WarmUpRegistrationLifeCycle;

@Configuration
@EnableConfigurationProperties
@ConditionalOnClass(EurekaClientConfig.class)
@ConditionalOnProperty(value = "eureka.client.enabled", matchIfMissing = true)
@ConditionalOnMissingClass("org.springframework.cloud.gateway.filter.factory.GatewayFilterFactory")
@AutoConfigureAfter(name = {
		"org.springframework.cloud.netflix.eureka.EurekaClientAutoConfiguration"})
@Slf4j
public class VEurekaClientAutoConfiguration {


	@Autowired
	Environment environment;

	@Bean
	@ConditionalOnBean(VAutoServiceRegistrationProperties.class)
	@ConditionalOnProperty(
			value = "v.spring.cloud.service-registry.auto-registration.enabled",
			matchIfMissing = true)
	public EurekaRegistration eurekaRegistration(EurekaClient eurekaClient,
			CloudEurekaInstanceConfig instanceConfig,
			ApplicationInfoManager applicationInfoManager, @Autowired(
			required = false) ObjectProvider<HealthCheckHandler> healthCheckHandler) {
		return EurekaRegistration.builder(instanceConfig).with(applicationInfoManager)
				.with(eurekaClient).with(healthCheckHandler).build();
	}

    @ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
    public class ServletAutoConfiguration {

        @Bean
        @ConditionalOnBean(VAutoServiceRegistrationProperties.class)
        @ConditionalOnMissingBean(GracefulEurekaAutoServiceRegistration.class)
        @ConditionalOnProperty(value = "v.spring.cloud.service-registry.auto-registration.enabled", matchIfMissing = true)
        public EurekaAutoServiceRegistration gracefulEurekaAutoServiceRegistration(
            ApplicationContext context, EurekaServiceRegistry registry,
            EurekaRegistration registration,@Autowired(required = false) List<WarmUp> warmUps,VAutoServiceRegistrationProperties properties) {
            GracefulStartInfoManager.getInstance().setDelayRegistryMillis(properties.getDelayRegistryMillis());
            GracefulStartInfoManager.getInstance().setExecuteWarmUpLogicTimes(properties.getExecuteWarmUpLogicTimes());
            String host = environment.getProperty("HOST");
            String qaePort = environment.getProperty(properties.getQaeHttpPortName());
            String tomPort = environment.getProperty("server.port","8080");
            qaePort = qaePort == null ? tomPort:qaePort;
            GracefulStartInfoManager.getInstance().setHost(host == null ? "localhost":host);
            GracefulStartInfoManager.getInstance().setPort(Integer.parseInt(qaePort));
            GracefulStartInfoManager.getInstance().setTomcatServerPort(Integer.parseInt(tomPort == null ? "8080":tomPort));
            GracefulStartInfoManager.getInstance().setPathList(properties.getWarmUpPathList());
            GracefulStartInfoManager.getInstance().setGracefulStartEnabled(properties.getGracefulStartEnabled());
            return new GracefulEurekaAutoServiceRegistration(context, registry, registration,warmUps,properties.getWarmUpTimeout(),Integer.parseInt(qaePort));
        }

        @Bean
        @ConditionalOnBean({ VAutoServiceRegistrationProperties.class,
            ServiceGracefulShutDownProperties.class })
        @ConditionalOnProperty(value = "v.spring.cloud.service-registry.auto-registration.enabled", matchIfMissing = true)
        public InterceptorConfig interceptorConfig(
            ServiceGracefulShutDownProperties properties) {
            GracefulShutDownInfoManager.getInstance()
                .setWaitTimeoutMillis(properties.getWaitTimeoutMillis());
            String excludePathPatterns = properties.getExcludePathPatterns();
            String includePathPatterns = properties.getIncludePathPatterns();
            String[] includePathPatternsArr = includePathPatterns.split("\\,");
            String[] excludePathPatternsArr = excludePathPatterns.split("\\,");
            GracefulShutDownInfoManager.getInstance()
                .setExcludePathPatterns(excludePathPatternsArr);
            GracefulShutDownInfoManager.getInstance()
                .setIncludePathPatterns(includePathPatternsArr);
            GracefulShutDownInfoManager.getInstance()
                .setGracefulShutDownEnabled(properties.isEnabled());
            GracefulShutDownInterceptor gracefulShutDownInterceptor = new GracefulShutDownInterceptor();
            log.info("已支持优雅下线。");
            return new InterceptorConfig(gracefulShutDownInterceptor, includePathPatternsArr,
                excludePathPatternsArr);
        }
    }


    @ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.REACTIVE)
    public class ReactiveAutoConfiguration {

        private final Logger LOGGER = LoggerFactory.getLogger(ReactiveAutoConfiguration.class);

        @Bean
        @ConditionalOnBean(VAutoServiceRegistrationProperties.class)
        @ConditionalOnProperty(value = "v.spring.cloud.service-registry.auto-registration.enabled", matchIfMissing = true)
        public EurekaAutoServiceRegistration eurekaAutoServiceRegistration(ApplicationContext context,
            EurekaServiceRegistry registry, EurekaRegistration registration) {
            return new VipEurekaAutoServiceRegistration(context, registry, registration);
        }

        @Bean
        public GracefulShutDownWebFilter gracefulShutDownFilter(ServiceGracefulShutDownProperties properties) {
            LOGGER.info("已支持优雅下线。");
            String[] includePathPatternsArr = properties.getIncludePathPatterns().split(",");
            String[] excludePathPatternsArr = properties.getExcludePathPatterns().split(",");
            return new GracefulShutDownWebFilter(includePathPatternsArr, excludePathPatternsArr);
        }

        @Bean
        public ShutDownEventListener shutDownEventListener(List<AutoServiceRegistration> autoServiceRegistrations,
            ServiceGracefulShutDownProperties properties, ApplicationContext context) {
            return new ShutDownEventListener(autoServiceRegistrations, properties, context);
        }

        @Bean
        @ConditionalOnMissingBean
        public WarmUpRegistrationLifeCycle warmUpRegistrationLifeCycle(List<WarmUpAction> warmUpActions) {
            return new WarmUpRegistrationLifeCycle(warmUpActions);
        }
    }

}
