package com.iqiyi.v.springframwork.cloud.netflix.eureka.serviceregistry.webflux;

/**
 * <AUTHOR>
 * @date 2023/5/23 17:49
 */
public class AppInfoManager {

    private static final AppInfoManager INSTANCE = new AppInfoManager();

    private volatile boolean stopping;

    public static AppInfoManager getInstance() {
        return INSTANCE;
    }

    public boolean isStopping() {
        return stopping;
    }

    public void setStopping(boolean stopping) {
        this.stopping = stopping;
    }
}
