package com.iqiyi.v.springframwork.cloud.netflix.ext.webClient;

import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.loadbalancer.reactive.ReactiveLoadBalancer;
import org.springframework.cloud.loadbalancer.support.LoadBalancerClientFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.web.reactive.function.client.ClientRequest;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.ExchangeFunction;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.util.List;

import com.iqiyi.v.springframwork.cloud.client.constants.VEurekaClientConstants;
import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.VRibbonHttpContext;
import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.loadbalance.core.VRoundRobinLoadBalancer;
import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.p.VLoadBalanceProperties;
import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.utils.VEvictionServers;

/**
 * An {@link ExchangeFilterFunction} that uses {@link ReactiveLoadBalancer} to execute requests against a correct {@link ServiceInstance}.
 *
 * <AUTHOR> Maciaszek-Sharma
 * @since 2.2.0
 */
@SuppressWarnings({"rawtypes", "unchecked"})
public class VResponseStatusUpdateUpServerListExchangeFilterFunction implements ExchangeFilterFunction {

    private static final Log LOG = LogFactory.getLog(VResponseStatusUpdateUpServerListExchangeFilterFunction.class);

    private LoadBalancerClientFactory loadBalancerClientFactory;
    private VLoadBalanceProperties vLoadBalanceProperties;

    public VResponseStatusUpdateUpServerListExchangeFilterFunction(LoadBalancerClientFactory loadBalancerClientFactory, VLoadBalanceProperties vLoadBalanceProperties) {
        this.loadBalancerClientFactory = loadBalancerClientFactory;
        this.vLoadBalanceProperties = vLoadBalanceProperties;
    }

    @Override
    public Mono<ClientResponse> filter(ClientRequest clientRequest, ExchangeFunction next) {

        return next.exchange(clientRequest)
            .doOnSuccess((clientResponse) -> {
                try {

                    if (clientResponse == null) {
                        return;
                    }
                    final Integer rawStatusCode;
                    List<String> list = clientResponse.headers().header(VEurekaClientConstants.RES_INSTANCE_STATUS_KEY);
                    rawStatusCode = !CollectionUtils.isEmpty(list) && NumberUtils.isDigits(list.get(0)) ?
                        Integer.parseInt(list.get(0)) : clientResponse.rawStatusCode();
                    if (VEvictionServers.isEviction(rawStatusCode)) {
                        ServiceInstance serviceInstance = VRibbonHttpContext.getServiceInstance(clientRequest);
                        if (serviceInstance == null) {
                            return;
                        }
                        ReactiveLoadBalancer<ServiceInstance> loadBalancer =
                            loadBalancerClientFactory.getInstance(serviceInstance.getServiceId());
                        if (loadBalancer instanceof VRoundRobinLoadBalancer) {
                            URI requestRouteUrl = clientRequest.url();
                            VEvictionServers.evictionServ(serviceInstance.getServiceId(), serviceInstance.getHost(),
                                serviceInstance.getPort(), requestRouteUrl, rawStatusCode, loadBalancer,
                                vLoadBalanceProperties);
                        }
                    }
                } catch (Exception e) {
                    LOG.error("v-response status update up server list, exception.", e);
                }
            });
    }

}
