package com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.utils;

import org.springframework.cloud.gateway.route.Route;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.AbstractServerHttpResponse;
import org.springframework.web.server.ServerWebExchange;

import java.net.URI;
import java.util.Set;

import static org.springframework.cloud.gateway.support.ServerWebExchangeUtils.GATEWAY_ORIGINAL_REQUEST_URL_ATTR;
import static org.springframework.cloud.gateway.support.ServerWebExchangeUtils.GATEWAY_REQUEST_URL_ATTR;
import static org.springframework.cloud.gateway.support.ServerWebExchangeUtils.GATEWAY_ROUTE_ATTR;
import static org.springframework.cloud.gateway.support.ServerWebExchangeUtils.GATEWAY_SCHEME_PREFIX_ATTR;

/**
 * <AUTHOR>
 * @date 2022/5/7 09:33
 */
public final class VServerWebExchangeUtils {

    /**
     * 请求URL <br/>
     * <p></p>
     * 对于微服务有两个阶段:<br/>
     * <p></p>
     * <li>1. 当loadbalance.choose前，存入的是LB的host，例如：VIP-TASK-CENTER-API /li>
     * <li>2. 当loadbalance.choose后，存入的是真实调用的URL，有具体的机器的ip地址替换了域名，就缺失了LB的host。</li>
     * @param exchange
     * @return
     */
    public static URI gatewayRequestUrl(ServerWebExchange exchange) {
        return exchange.getAttribute(GATEWAY_REQUEST_URL_ATTR);
    }

    public static Route gatewayRoute(ServerWebExchange exchange) {
        return (Route) exchange.getAttribute(GATEWAY_ROUTE_ATTR);
    }

    /**
     * 原URL集合
     * @param exchange
     * @return
     */
    public static Set<URI> gatewayOriginalRequestUrl(ServerWebExchange exchange) {
        return exchange.getAttribute(GATEWAY_ORIGINAL_REQUEST_URL_ATTR);
    }

    public static Object gatewaySchemePrefix(ServerWebExchange exchange) {
        return exchange.getAttribute(GATEWAY_SCHEME_PREFIX_ATTR);
    }

    /**
     * 原状态码
     * @param exchange
     * @return
     */
    public static Integer getRawStatusCode(ServerWebExchange exchange) {
        if (exchange.getResponse() instanceof AbstractServerHttpResponse) {
            return ((AbstractServerHttpResponse) exchange.getResponse()).getStatusCodeValue();
        }
        HttpStatus httpStatus = exchange.getResponse().getStatusCode();
        return (httpStatus != null ? httpStatus.value() : null);
    }

    /**
     * 用户请求URI
     * @param exchange
     * @return
     */
    public static URI requestUrl(ServerWebExchange exchange) {
        return exchange.getRequest().getURI();
    }

    private VServerWebExchangeUtils() {
    }
}
