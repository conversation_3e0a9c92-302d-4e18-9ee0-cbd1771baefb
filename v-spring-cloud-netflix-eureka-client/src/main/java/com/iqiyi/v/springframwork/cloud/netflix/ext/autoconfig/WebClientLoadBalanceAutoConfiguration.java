package com.iqiyi.v.springframwork.cloud.netflix.ext.autoconfig;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.loadbalancer.LoadBalancerClientsProperties;
import org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerClientRequestTransformer;
import org.springframework.cloud.client.loadbalancer.reactive.ReactiveLoadBalancer;
import org.springframework.cloud.loadbalancer.annotation.LoadBalancerClients;
import org.springframework.cloud.loadbalancer.support.LoadBalancerClientFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.web.reactive.function.client.WebClient;

import com.iqiyi.v.springframwork.cloud.netflix.ext.ribbon.business.p.VLoadBalanceProperties;
import com.iqiyi.v.springframwork.cloud.netflix.ext.webClient.GracefulWebClientCustomizer;
import com.iqiyi.v.springframwork.cloud.netflix.ext.webClient.VHeaderTransformer;

@Slf4j
@LoadBalancerClients(defaultConfiguration = LoadBalanceConfiguration.class)
@EnableConfigurationProperties({VLoadBalanceProperties.class, LoadBalancerClientsProperties.class})
@ConditionalOnBean(ReactiveLoadBalancer.Factory.class)
@ConditionalOnClass(WebClient.class)
public class WebClientLoadBalanceAutoConfiguration {

    @Bean
    public GracefulWebClientCustomizer gracefulWebClientCustomizer(VLoadBalanceProperties vLoadBalanceProperties, LoadBalancerClientFactory loadBalancerClientFactory){
        return new GracefulWebClientCustomizer(vLoadBalanceProperties, loadBalancerClientFactory);
    }


    @Bean
    public LoadBalancerClientRequestTransformer vHeaderTransformer() {
        return new VHeaderTransformer();
    }

}  