<?xml version="1.0" encoding="UTF-8"?>
<included>
    <appender name="VIP_LOADBLANCE_EXT_APP_ACCESS_LOG_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logging.path}/logback-qiyi-netflix-ext-graceful.log</file>
        <encoder>
            <pattern>[%-5p] [%d{yyyy-MM-dd HH:mm:ss.SSS}] [%t] [%C{1}:%M:%L] %m%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${logging.path}/logback-qiyi-netflix-ext-graceful.log.%d{yyyy-MM-dd_HH}</fileNamePattern>
            <maxHistory>15</maxHistory>
        </rollingPolicy>
    </appender>

    <appender name="VIP_LOADBLANCE_EXT_ASYNC_DAILY_ROLLING_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>512</queueSize>
        <includeCallerData>true</includeCallerData>
        <neverBlock>true</neverBlock>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref="VIP_LOADBLANCE_EXT_APP_ACCESS_LOG_FILE"/>
    </appender>

    <logger name="com.iqiyi.v.springframwork.cloud" additivity="false">
        <appender-ref ref="VIP_LOADBLANCE_EXT_ASYNC_DAILY_ROLLING_FILE"/>
    </logger>
</included>