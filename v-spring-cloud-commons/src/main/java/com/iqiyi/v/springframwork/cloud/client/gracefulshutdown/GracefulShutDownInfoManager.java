package com.iqiyi.v.springframwork.cloud.client.gracefulshutdown;

import java.util.concurrent.atomic.AtomicInteger;

import com.iqiyi.v.springframwork.cloud.client.constants.VDefinedHttpStatus;

public class GracefulShutDownInfoManager {

	/**
	 * 服务正在关闭http状态码
	 */
	public static final int SERVER_IS_STOPPING_HTTP_STATUS = VDefinedHttpStatus.SERVER_CLOSING_HTTP_STATUS.getStatus();

	/**
	 * 正在关闭标记
	 */
	private volatile boolean isStopping;

	/**
	 * 服务端停机等待时长默认5s
	 */
	private int waitTimeoutMillis;

	/**
	 * 优雅停机拦截器 忽略的拦截路径
	 */
	private String[] excludePathPatterns;

	/**
	 * 优雅停机拦截器 拦截路径
	 */
	private String[] includePathPatterns = new String[] { "/**" };

	/**
	 * 正在处理的请求数量
	 */
	private AtomicInteger handlingReqCount = new AtomicInteger(0);

	private boolean gracefulShutDownEnabled;

	/**
	 * 正在关闭标记test
	 */
	private volatile boolean isStopping4Test;


	public void setStopping() {
		isStopping = true;
	}

	public boolean isStopping() {
		return isStopping;
	}

	public void setWaitTimeoutMillis(int waitTimeoutMillis) {
		this.waitTimeoutMillis = waitTimeoutMillis;
	}

	public int getWaitTimeoutMillis() {
		return waitTimeoutMillis;
	}

	public int incrementReqCount() {
		return handlingReqCount.incrementAndGet();
	}

	public int decrymentReqCount() {
		return handlingReqCount.decrementAndGet();
	}

	public String[] getExcludePathPatterns() {
		return excludePathPatterns;
	}

	public void setExcludePathPatterns(String[] excludePathPatterns) {
		this.excludePathPatterns = excludePathPatterns;
	}

	public String[] getIncludePathPatterns() {
		return includePathPatterns;
	}

	public void setIncludePathPatterns(String[] includePathPatterns) {
		this.includePathPatterns = includePathPatterns;
	}

	public int getHandlingReqCount() {
		return this.handlingReqCount.get();
	}


	public boolean isStopping4Test() {
		return isStopping4Test;
	}

	public void setStopping4Test(boolean stopping4Test) {
		isStopping4Test = stopping4Test;
	}

	public boolean isGracefulShutDownEnabled() {
		return gracefulShutDownEnabled;
	}

	public void setGracefulShutDownEnabled(boolean gracefulShutDownEnabled) {
		this.gracefulShutDownEnabled = gracefulShutDownEnabled;
	}

	public static GracefulShutDownInfoManager getInstance() {
		return SingleTonManager.INSTANCE;
	}

	private static class SingleTonManager {

		private static final GracefulShutDownInfoManager INSTANCE = new GracefulShutDownInfoManager();

	}

}
