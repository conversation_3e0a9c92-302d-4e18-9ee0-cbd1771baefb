package com.iqiyi.v.springframwork.cloud.client.serviceregistry;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MapPropertySource;;

@Order
public class DiscoveryEnvironmentPostProcessor implements EnvironmentPostProcessor {
    @Override
    public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {
        Map<String, Object> autoRegistration = new HashMap<>();
        autoRegistration.put("spring.cloud.service-registry.auto-registration.enabled", "false");
        environment.getPropertySources().addFirst(
                new MapPropertySource("spring-cloud-iqiyi-auto-registration", autoRegistration));
    }
}
