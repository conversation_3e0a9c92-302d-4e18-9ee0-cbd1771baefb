/*
 * Copyright 2013-2019 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.iqiyi.v.springframwork.cloud.client.constants;

/**
 *
 */
public final class VEurekaClientConstants {

	public static final String WEIGHT_KEY = "weight";
	public static final int DEFAULT_WEIGHT = 1;
	public static final String START_TIMESTAMP_KEY = "startTimestamp";

	public static final String FLOW_WARM_UP_TIME_SECONDS_KEY = "flowWarmUpTimeSeconds";

	public static final int DEFAULT_FLOW_WARM_UP_TIME_SECONDS = 2 * 60;

	public static final int MIN_FLOW_WARM_UP_TIME_SECONDS = 60;

	public static final String RES_INSTANCE_STATUS_KEY = "res-instance-status";

	public static final String RES_INSTANCE_STATUS_VALUE = String.valueOf(VDefinedHttpStatus.SERVER_CLOSING_HTTP_STATUS.getStatus());

	public static final String ZONE_PRE = "zone-pre";

}
