package com.iqiyi.v.springframwork.cloud.client.gracefulshutdown.Interceptor;

import com.iqiyi.v.springframwork.cloud.client.constants.VEurekaClientConstants;
import com.iqiyi.v.springframwork.cloud.client.gracefulshutdown.GracefulShutDownInfoManager;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class GracefulShutDownInterceptor implements HandlerInterceptor {

	private static final Log log = LogFactory.getLog(GracefulShutDownInterceptor.class);

	@Override
	public boolean preHandle(HttpServletRequest httpServletRequest,
			HttpServletResponse httpServletResponse, Object o) throws Exception {
		boolean isStopping = GracefulShutDownInfoManager.getInstance().isStopping();
		if (isStopping) {
			log.info("server isStopping");
			httpServletResponse.setHeader(VEurekaClientConstants.RES_INSTANCE_STATUS_KEY, VEurekaClientConstants.RES_INSTANCE_STATUS_VALUE);
//			httpServletResponse
//					.setStatus(GracefulShutDownInfoManager.SERVER_IS_STOPPING_HTTP_STATUS);
//			return false;
		}
		return true;
	}

	@Override
	public void postHandle(HttpServletRequest httpServletRequest,
			HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView)
			throws Exception {
	}

	@Override
	public void afterCompletion(HttpServletRequest httpServletRequest,
			HttpServletResponse httpServletResponse, Object o, Exception e)
			throws Exception {

	}

}
