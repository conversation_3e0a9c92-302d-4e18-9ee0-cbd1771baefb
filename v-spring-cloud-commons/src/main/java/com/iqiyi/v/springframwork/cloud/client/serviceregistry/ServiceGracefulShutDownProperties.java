package com.iqiyi.v.springframwork.cloud.client.serviceregistry;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 */
@ConfigurationProperties("v.spring.cloud.service-registry.graceful-shutdown")
public class ServiceGracefulShutDownProperties {

	private static final Log LOG = LogFactory.getLog(ServiceGracefulShutDownProperties.class);

	private int waitTimeoutMillis = 20000;

	private String excludePathPatterns = "/health,/*error*,/index";

	private String includePathPatterns = "/**";

	private boolean enabled = true;

    public void setWaitTimeoutMillis(int waitTimeoutMillis) {
        if (waitTimeoutMillis >= 20000) {
            this.waitTimeoutMillis = waitTimeoutMillis;
        } else {
            LOG.warn("waitTimeoutMillis can't be configured less than 20000, so it's set to 20000 instead");
            this.waitTimeoutMillis = 20000;
        }
    }

	public int getWaitTimeoutMillis() {
		return waitTimeoutMillis;
	}

	public String getExcludePathPatterns() {
		return excludePathPatterns;
	}

	public void setExcludePathPatterns(String excludePathPatterns) {
		this.excludePathPatterns = excludePathPatterns;
	}

	public void setIncludePathPatterns(String includePathPatterns) {
		this.includePathPatterns = includePathPatterns;
	}

	public String getIncludePathPatterns() {
		return includePathPatterns;
	}

	public boolean isEnabled() {
		return enabled;
	}

	public void setEnabled(boolean enabled) {
		this.enabled = enabled;
	}
}
