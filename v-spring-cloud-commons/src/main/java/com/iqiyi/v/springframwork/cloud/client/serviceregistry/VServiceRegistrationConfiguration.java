package com.iqiyi.v.springframwork.cloud.client.serviceregistry;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
@EnableConfigurationProperties(ServiceGracefulShutDownProperties.class)
@ConditionalOnProperty(value = "v.spring.cloud.service-registry.auto-registration.enabled",
		matchIfMissing = true)
public class VServiceRegistrationConfiguration {

}
