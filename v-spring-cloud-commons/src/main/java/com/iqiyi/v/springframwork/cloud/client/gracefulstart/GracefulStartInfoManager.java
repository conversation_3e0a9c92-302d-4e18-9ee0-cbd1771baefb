package com.iqiyi.v.springframwork.cloud.client.gracefulstart;

import java.util.LinkedList;
import java.util.List;

public class GracefulStartInfoManager {

	/**
	 * 服务端延迟注册时长默认5s
	 */
	private int delayRegistryMillis;

	private List<String> pathList = new LinkedList<>();

	private String host;

	private int port;

	private boolean gracefulStartEnabled;

	private int tomcatServerPort;

	private volatile boolean warmedUp;

	private int executeWarmUpLogicTimes;

	public int getTomcatServerPort() {
		return tomcatServerPort;
	}

	public void setTomcatServerPort(int tomcatServerPort) {
		this.tomcatServerPort = tomcatServerPort;
	}



	public static GracefulStartInfoManager getInstance() {
		return SingleTonManager.INSTANCE;
	}

	private static class SingleTonManager {

		private static final GracefulStartInfoManager INSTANCE = new GracefulStartInfoManager();

	}

	public int getDelayRegistryMillis() {
		return delayRegistryMillis;
	}

    public 	void setDelayRegistryMillis(int delayRegistryMillis) {
		this.delayRegistryMillis = delayRegistryMillis;
	}

	public List<String> getPathList() {
		return pathList;
	}

	public void setPathList(List<String> pathList) {
		this.pathList = pathList;
	}

	public	String getHost() {
		return host;
	}

	public void setHost(String host) {
		this.host = host;
	}

	public int getPort() {
		return port;
	}

	public void setPort(int port) {
		this.port = port;
	}

	public boolean isGracefulStartEnabled() {
		return gracefulStartEnabled;
	}

	public void setGracefulStartEnabled(boolean gracefulStartEnabled) {
		this.gracefulStartEnabled = gracefulStartEnabled;
	}

	public boolean isWarmedUp() {
		return warmedUp;
	}

	public void setWarmedUp(boolean warmedUp) {
		this.warmedUp = warmedUp;
	}

	public int getExecuteWarmUpLogicTimes() {
		return executeWarmUpLogicTimes;
	}

	public void setExecuteWarmUpLogicTimes(int executeWarmUpLogicTimes) {
		this.executeWarmUpLogicTimes = executeWarmUpLogicTimes;
	}
}
