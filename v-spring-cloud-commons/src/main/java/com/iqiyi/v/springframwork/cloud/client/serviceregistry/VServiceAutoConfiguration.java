package com.iqiyi.v.springframwork.cloud.client.serviceregistry;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * <AUTHOR>
 */
@Configuration
@Import(VServiceRegistrationConfiguration.class)
@ConditionalOnProperty(value = "v.spring.cloud.service-registry.auto-registration.enabled",
		matchIfMissing = true)
public class VServiceAutoConfiguration {

}
