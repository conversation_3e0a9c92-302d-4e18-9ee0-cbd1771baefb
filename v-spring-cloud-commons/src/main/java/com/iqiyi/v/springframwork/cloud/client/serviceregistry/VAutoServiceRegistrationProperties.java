/*
 * Copyright 2012-2019 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.iqiyi.v.springframwork.cloud.client.serviceregistry;

import java.util.LinkedList;
import java.util.List;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 */
@ConfigurationProperties("v.spring.cloud.service-registry.auto-registration")
public class VAutoServiceRegistrationProperties {

	private boolean enabled = true;

	private int warmUpTimeout = 20 * 1000;

	private int delayRegistryMillis = 10 * 1000;

    private String qaeHttpPortName = "PORT_8080";

	private List<String> warmUpPathList = new LinkedList<>();

	private boolean gracefulStartEnabled = true;

	private int executeWarmUpLogicTimes = 10;

	public boolean isEnabled() {
		return this.enabled;
	}

	public void setEnabled(boolean enabled) {
		this.enabled = enabled;
	}

	public int getWarmUpTimeout() {
		return warmUpTimeout;
	}

	public void setWarmUpTimeout(int warmUpTimeout) {
		this.warmUpTimeout = warmUpTimeout;
	}

	public int getDelayRegistryMillis() {
		return delayRegistryMillis;
	}

	public void setDelayRegistryMillis(int delayRegistryMillis) {
		this.delayRegistryMillis = delayRegistryMillis;
	}

    public String getQaeHttpPortName() {
        return qaeHttpPortName;
    }

    public void setQaeHttpPortName(String qaeHttpPortName) {
        this.qaeHttpPortName = qaeHttpPortName;
    }

    public List<String> getWarmUpPathList() {
		return warmUpPathList;
	}

	public void setWarmUpPathList(List<String> warmUpPathList) {
		this.warmUpPathList = warmUpPathList;
	}

	public boolean getGracefulStartEnabled() {
		return gracefulStartEnabled;
	}

	public void setGracefulStartEnabled(boolean gracefulStartEnabled) {
		this.gracefulStartEnabled = gracefulStartEnabled;
	}

	public int getExecuteWarmUpLogicTimes() {
		return executeWarmUpLogicTimes;
	}

	public void setExecuteWarmUpLogicTimes(int executeWarmUpLogicTimes) {
		this.executeWarmUpLogicTimes = executeWarmUpLogicTimes;
	}
}

