# å½åæ ¼å¼ï¼åºç¨å-online
spring.application.name=test-v-spring-cloud-netflix-eureka-client
zone.port=8080

# å¨ä½çæ¬ä¸­ä½¿ç¨çæ¯ ${spring.cloud.client.ipAddress}ï¼å¯ä»¥çä¸org.springframework.cloud.client.HostInfoEnvironmentPostProcessorè¿ä¸ªç±»ä¸­postProcessEnvironmentæ¹æ³ä½¿ç¨çæ¯åªä¸ªå±æ§ï¼ä¿æä¸è´å³å¯
eureka.instance.hostname=${spring.cloud.client.ip-address}
eureka.instance.instance-id=${eureka.instance.hostname}:${zone.port}

# åºç¨æå¨zoneï¼æ ¹æ®åºç¨æºæ¿å¡«å
eureka.instance.metadata-map.zone=zone-bj
# ç§ææ´æ°æ¶é´é´éï¼é»è®¤30ç§ï¼
eureka.instance.lease-renewal-interval-in-seconds=5
# ç§æå°ææ¶é´ï¼é»è®¤90ç§ï¼
eureka.instance.lease-expiration-duration-in-seconds=15
# æ³¨åä¸­å¿éç½®
eureka.client.serviceUrl.defaultZone=http://test-eureka.vip.qiyi.domain:8080/eureka
#eureka.client.serviceUrl.defaultZone=http://eureka.vip.qiyi.domain:8080/eureka/
# å³é­Netflixèªå·±çææ æ¶é
spring.cloud.netflix.metrics.enabled=false

v.spring.cloud.service-registry.auto-registration.enabled=true

eureka.client.registryFetchIntervalSeconds=5

spring.main.allow-bean-definition-overriding=true

v-load.balance.props.least-server-count=0
v-load.balance.props.self-preservation-rate=0
v-load.balance.props.ping-interval-seconds=10

eureka.client.healthcheck.enabled=false

vipinfo.first_vip_info.url=http://VIP-INFO-SERVER-ONLINE/external/users/first_vip_info