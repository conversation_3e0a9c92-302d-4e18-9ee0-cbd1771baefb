package com.iqiyi.v.test.controller;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/11/6 21:17
 */
@RequestMapping("/test")
@RestController
public class TestController {

    @Value("${vipinfo.first_vip_info.url}")
    private String firstVipInfoUrl;

    @Resource
    private RestTemplate lbRestTemplate;

    @RequestMapping(value ="/1", produces = "application/json")
    public String testFirstVipInfo(){
        return lbRestTemplate.getForEntity(firstVipInfoUrl + "?appVersion=14.5.0&bizSource=kefu&messageId=123e4064135ab19fbf8ed3f0a91102&deviceId=f6e44feefe4064135ab19fbf8ed3f0a91102&version=5.0&platform=01010021010000000000&suid=APiDTVkYMAGUfYV_p00UUkg&P00001=bfscK2vrsWJvjm1dDPZT9CjDSYTR8kZAKu0uKpQm2XV9FRFK1wDdrba3reI1t1rpp76Dc9", String.class).getBody();
    }

    @RequestMapping(value ="/2")
    public String test1(){
        return lbRestTemplate.getForEntity("http://service-provider/test", String.class).getBody();
    }

    @RequestMapping(value ="/3")
    public String test3(){
        return lbRestTemplate.getForEntity("http://VIP-TAG-PLAT-ONLINE/actuator/health", String.class).getBody();
    }

}

