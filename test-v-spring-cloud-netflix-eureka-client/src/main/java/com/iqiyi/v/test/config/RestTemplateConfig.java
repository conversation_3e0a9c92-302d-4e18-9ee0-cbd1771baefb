package com.iqiyi.v.test.config;

import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @date 2023/11/6 21:16
 */
@Configuration
public class RestTemplateConfig {

    @Bean(name="lbRestTemplate")
    @LoadBalanced
    RestTemplate lbRestTemplate() {
        // 可以根据实际情况自定义 RestTemplate 相关配置（例如：超时时间），这里为了简单直接创建了一个 RestTemplate 对象
        return new RestTemplate();
    }

}

