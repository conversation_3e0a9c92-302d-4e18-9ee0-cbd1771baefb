<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>2.7.8</version>
    <relativePath/> <!-- lookup parent from repository -->
  </parent>

  <groupId>org.example</groupId>
  <artifactId>test-v-spring-cloud-netflix-eureka-client</artifactId>
  <version>1.0-SNAPSHOT</version>

  <properties>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
    <v.spring-cloud.version>v-1.3.1-boot-2.7.x-SNAPSHOT</v.spring-cloud.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>com.iqiyi.v</groupId>
      <artifactId>v-spring-cloud-netflix-eureka-client</artifactId>
    </dependency>
  </dependencies>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.iqiyi.v</groupId>
        <artifactId>v-spring-cloud-netflix</artifactId>
        <version>${v.spring-cloud.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <repositories>
    <repository>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>libs-release</id>
      <name>libs-release</name>
      <url>http://jfrog.cloud.qiyi.domain/libs-release</url>
    </repository>
    <repository>
      <snapshots/>
      <id>libs-snapshot</id>
      <name>libs-snapshot</name>
      <url>http://jfrog.cloud.qiyi.domain/libs-snapshot</url>
    </repository>
    <repository>
      <snapshots/>
      <id>iqiyi-maven-cloudservice</id>
      <name>iqiyi-maven-cloudservice</name>
      <url>http://jfrog.cloud.qiyi.domain/iqiyi-maven-cloudservice</url>
    </repository>
  </repositories>


</project>